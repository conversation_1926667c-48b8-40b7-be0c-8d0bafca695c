-- Sales Management System Database Setup
-- Run this script to create the database and tables

CREATE DATABASE IF NOT EXISTS shop_management;
USE shop_management;

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    role ENUM('admin', 'cashier') NOT NULL DEFAULT 'cashier',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Categories table
CREATE TABLE IF NOT EXISTS categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Products table
CREATE TABLE IF NOT EXISTS products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    barcode VARCHAR(100) UNIQUE,
    category_id INT,
    price DECIMAL(10,2) NOT NULL,
    cost DECIMAL(10,2) NOT NULL,
    stock INT NOT NULL DEFAULT 0,
    min_stock INT NOT NULL DEFAULT 5,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL
);

-- Customers table
CREATE TABLE IF NOT EXISTS customers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(20),
    address TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Sales table
CREATE TABLE IF NOT EXISTS sales (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    customer_id INT,
    total_amount DECIMAL(10,2) NOT NULL,
    amount_received DECIMAL(10,2) NOT NULL,
    change_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
    discount DECIMAL(10,2) NOT NULL DEFAULT 0,
    payment_type ENUM('cash', 'card', 'mobile') NOT NULL DEFAULT 'cash',
    sale_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL
);

-- Sale items table
CREATE TABLE IF NOT EXISTS sale_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    sale_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity INT NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    total DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (sale_id) REFERENCES sales(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);

-- Insert default admin user (password: admin123)
INSERT INTO users (username, password, full_name, email, role) VALUES 
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System Administrator', '<EMAIL>', 'admin')
ON DUPLICATE KEY UPDATE username = username;

-- Insert default cashier user (password: cashier123)
INSERT INTO users (username, password, full_name, email, role) VALUES 
('cashier', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Default Cashier', '<EMAIL>', 'cashier')
ON DUPLICATE KEY UPDATE username = username;

-- Insert sample categories
INSERT INTO categories (name, description) VALUES 
('Electronics', 'Electronic devices and accessories'),
('Clothing', 'Apparel and fashion items'),
('Food & Beverages', 'Food items and drinks'),
('Books', 'Books and educational materials'),
('Home & Garden', 'Home improvement and garden supplies')
ON DUPLICATE KEY UPDATE name = name;

-- Insert sample products
INSERT INTO products (name, description, barcode, category_id, price, cost, stock, min_stock) VALUES 
('Smartphone', 'Latest model smartphone', '1234567890123', 1, 599.99, 400.00, 25, 5),
('Laptop', 'High-performance laptop', '1234567890124', 1, 999.99, 700.00, 15, 3),
('T-Shirt', 'Cotton t-shirt', '1234567890125', 2, 19.99, 10.00, 50, 10),
('Jeans', 'Denim jeans', '1234567890126', 2, 49.99, 25.00, 30, 8),
('Coffee', 'Premium coffee beans', '1234567890127', 3, 12.99, 8.00, 100, 20),
('Novel', 'Bestselling novel', '1234567890128', 4, 14.99, 8.00, 40, 10),
('Garden Tool Set', 'Complete garden tool set', '1234567890129', 5, 79.99, 45.00, 20, 5)
ON DUPLICATE KEY UPDATE name = name;

-- Insert sample customers
INSERT INTO customers (name, email, phone, address) VALUES 
('John Doe', '<EMAIL>', '555-0101', '123 Main St, City, State'),
('Jane Smith', '<EMAIL>', '555-0102', '456 Oak Ave, City, State'),
('Bob Johnson', '<EMAIL>', '555-0103', '789 Pine Rd, City, State')
ON DUPLICATE KEY UPDATE name = name;

-- Create indexes for better performance
CREATE INDEX idx_products_barcode ON products(barcode);
CREATE INDEX idx_products_category ON products(category_id);
CREATE INDEX idx_sales_date ON sales(sale_date);
CREATE INDEX idx_sales_user ON sales(user_id);
CREATE INDEX idx_sale_items_sale ON sale_items(sale_id);
CREATE INDEX idx_sale_items_product ON sale_items(product_id);

-- Create views for reporting
CREATE OR REPLACE VIEW sales_summary AS
SELECT 
    s.id,
    s.sale_date,
    u.full_name as cashier_name,
    c.name as customer_name,
    s.total_amount,
    s.payment_type,
    COUNT(si.id) as items_count
FROM sales s
LEFT JOIN users u ON s.user_id = u.id
LEFT JOIN customers c ON s.customer_id = c.id
LEFT JOIN sale_items si ON s.id = si.sale_id
GROUP BY s.id;

CREATE OR REPLACE VIEW product_sales_summary AS
SELECT 
    p.id,
    p.name,
    p.price,
    p.stock,
    COALESCE(SUM(si.quantity), 0) as total_sold,
    COALESCE(SUM(si.total), 0) as total_revenue
FROM products p
LEFT JOIN sale_items si ON p.id = si.product_id
GROUP BY p.id;

CREATE OR REPLACE VIEW low_stock_products AS
SELECT 
    p.id,
    p.name,
    p.stock,
    p.min_stock,
    c.name as category_name
FROM products p
LEFT JOIN categories c ON p.category_id = c.id
WHERE p.stock <= p.min_stock;

-- Sample sales data (optional)
INSERT INTO sales (user_id, customer_id, total_amount, amount_received, change_amount, payment_type) VALUES 
(1, 1, 619.98, 650.00, 30.02, 'cash'),
(2, 2, 32.98, 35.00, 2.02, 'cash'),
(1, NULL, 79.99, 80.00, 0.01, 'card')
ON DUPLICATE KEY UPDATE id = id;

-- Sample sale items (optional)
INSERT INTO sale_items (sale_id, product_id, quantity, price, total) VALUES 
(1, 1, 1, 599.99, 599.99),
(1, 3, 1, 19.99, 19.99),
(2, 3, 1, 19.99, 19.99),
(2, 5, 1, 12.99, 12.99),
(3, 7, 1, 79.99, 79.99)
ON DUPLICATE KEY UPDATE id = id;
