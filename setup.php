<?php
// Database Setup Page
require_once 'includes/config.php';

$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        // Read SQL file
        $sql = file_get_contents('database_setup.sql');
        
        if ($sql === false) {
            throw new Exception('Could not read database_setup.sql file');
        }
        
        // Create database connection
        $database = new Database();
        $db = $database->getConnection();
        
        // Split SQL into individual statements
        $statements = array_filter(array_map('trim', explode(';', $sql)));
        
        $executed = 0;
        foreach ($statements as $statement) {
            if (!empty($statement)) {
                $stmt = $db->prepare($statement);
                $stmt->execute();
                $executed++;
            }
        }
        
        $message = "Database setup completed successfully! Executed {$executed} SQL statements.";
        
    } catch (Exception $e) {
        $error = "Setup failed: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Setup - Sales Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="bi bi-database"></i> Database Setup
                        </h4>
                    </div>
                    <div class="card-body">
                        <?php if ($message): ?>
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <i class="bi bi-check-circle"></i> <?php echo $message; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                            <div class="text-center">
                                <a href="login.php" class="btn btn-primary">
                                    <i class="bi bi-arrow-right"></i> Go to Login
                                </a>
                            </div>
                        <?php elseif ($error): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="bi bi-exclamation-triangle"></i> <?php echo $error; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!$message): ?>
                            <div class="mb-4">
                                <h5>Welcome to Sales Management System Setup</h5>
                                <p class="text-muted">
                                    This setup will create the necessary database tables and insert sample data 
                                    to get you started with the system.
                                </p>
                            </div>
                            
                            <div class="alert alert-info">
                                <h6><i class="bi bi-info-circle"></i> What will be created:</h6>
                                <ul class="mb-0">
                                    <li>Database tables (users, products, categories, customers, sales, sale_items)</li>
                                    <li>Default admin user (username: admin, password: admin123)</li>
                                    <li>Default cashier user (username: cashier, password: cashier123)</li>
                                    <li>Sample categories and products</li>
                                    <li>Sample customers</li>
                                </ul>
                            </div>
                            
                            <div class="alert alert-warning">
                                <h6><i class="bi bi-exclamation-triangle"></i> Important Notes:</h6>
                                <ul class="mb-0">
                                    <li>Make sure your database connection is configured in <code>includes/config.php</code></li>
                                    <li>This will create/overwrite existing data</li>
                                    <li>Change default passwords after setup</li>
                                </ul>
                            </div>
                            
                            <form method="POST" class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg" onclick="return confirm('Are you sure you want to proceed with the database setup?')">
                                    <i class="bi bi-play-circle"></i> Run Database Setup
                                </button>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="text-center mt-3">
                    <small class="text-muted">
                        Sales Management System v1.0
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
