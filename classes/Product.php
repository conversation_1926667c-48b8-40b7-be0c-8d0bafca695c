<?php
class Product {
    private $conn;
    private $table = 'products';
    
    public $id;
    public $name;
    public $description;
    public $barcode;
    public $category_id;
    public $price;
    public $cost;
    public $stock;
    public $min_stock;
    public $created_at;
    public $updated_at;
    
    public function __construct($db) {
        $this->conn = $db;
    }
    
    public function read() {
        $query = "SELECT p.*, c.name as category_name 
                  FROM " . $this->table . " p 
                  LEFT JOIN categories c ON p.category_id = c.id 
                  ORDER BY p.created_at DESC";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_OBJ);
    }
    
    public function readOne() {
        $query = "SELECT * FROM " . $this->table . " WHERE id = ? LIMIT 1";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $this->id);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_OBJ);
    }
    
    public function create() {
        $query = "INSERT INTO " . $this->table . " 
                  SET name = :name, 
                      description = :description, 
                      barcode = :barcode, 
                      category_id = :category_id, 
                      price = :price, 
                      cost = :cost, 
                      stock = :stock, 
                      min_stock = :min_stock";
        
        $stmt = $this->conn->prepare($query);
        
        $this->name = htmlspecialchars(strip_tags($this->name));
        $this->description = htmlspecialchars(strip_tags($this->description));
        $this->barcode = htmlspecialchars(strip_tags($this->barcode));
        $this->category_id = htmlspecialchars(strip_tags($this->category_id));
        $this->price = htmlspecialchars(strip_tags($this->price));
        $this->cost = htmlspecialchars(strip_tags($this->cost));
        $this->stock = htmlspecialchars(strip_tags($this->stock));
        $this->min_stock = htmlspecialchars(strip_tags($this->min_stock));
        
        $stmt->bindParam(':name', $this->name);
        $stmt->bindParam(':description', $this->description);
        $stmt->bindParam(':barcode', $this->barcode);
        $stmt->bindParam(':category_id', $this->category_id);
        $stmt->bindParam(':price', $this->price);
        $stmt->bindParam(':cost', $this->cost);
        $stmt->bindParam(':stock', $this->stock);
        $stmt->bindParam(':min_stock', $this->min_stock);
        
        if($stmt->execute()) {
            return true;
        }
        return false;
    }
    
    public function update() {
        $query = "UPDATE " . $this->table . " 
                  SET name = :name, 
                      description = :description, 
                      barcode = :barcode, 
                      category_id = :category_id, 
                      price = :price, 
                      cost = :cost, 
                      stock = :stock, 
                      min_stock = :min_stock 
                  WHERE id = :id";
        
        $stmt = $this->conn->prepare($query);
        
        $this->name = htmlspecialchars(strip_tags($this->name));
        $this->description = htmlspecialchars(strip_tags($this->description));
        $this->barcode = htmlspecialchars(strip_tags($this->barcode));
        $this->category_id = htmlspecialchars(strip_tags($this->category_id));
        $this->price = htmlspecialchars(strip_tags($this->price));
        $this->cost = htmlspecialchars(strip_tags($this->cost));
        $this->stock = htmlspecialchars(strip_tags($this->stock));
        $this->min_stock = htmlspecialchars(strip_tags($this->min_stock));
        $this->id = htmlspecialchars(strip_tags($this->id));
        
        $stmt->bindParam(':name', $this->name);
        $stmt->bindParam(':description', $this->description);
        $stmt->bindParam(':barcode', $this->barcode);
        $stmt->bindParam(':category_id', $this->category_id);
        $stmt->bindParam(':price', $this->price);
        $stmt->bindParam(':cost', $this->cost);
        $stmt->bindParam(':stock', $this->stock);
        $stmt->bindParam(':min_stock', $this->min_stock);
        $stmt->bindParam(':id', $this->id);
        
        if($stmt->execute()) {
            return true;
        }
        return false;
    }
    
    public function delete() {
        $query = "DELETE FROM " . $this->table . " WHERE id = ?";
        $stmt = $this->conn->prepare($query);
        $this->id = htmlspecialchars(strip_tags($this->id));
        $stmt->bindParam(1, $this->id);
        if($stmt->execute()) {
            return true;
        }
        return false;
    }
    
    public function count() {
        $query = "SELECT COUNT(*) as total FROM " . $this->table;
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row['total'];
    }
    
    public function getLowStock() {
        $query = "SELECT * FROM " . $this->table . " WHERE stock <= min_stock ORDER BY stock ASC";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_OBJ);
    }
    
    public function search($keyword) {
        $query = "SELECT * FROM " . $this->table . " 
                  WHERE name LIKE :keyword OR barcode = :barcode 
                  ORDER BY name";
        $stmt = $this->conn->prepare($query);
        
        $keyword = htmlspecialchars(strip_tags($keyword));
        $keyword = "%{$keyword}%";
        
        $stmt->bindParam(':keyword', $keyword);
        $stmt->bindParam(':barcode', $keyword);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_OBJ);
    }
}
?>