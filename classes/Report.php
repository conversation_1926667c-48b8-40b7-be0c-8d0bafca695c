<?php
class Report {
    private $conn;
    
    public function __construct($db) {
        $this->conn = $db;
    }
    
    public function getDailySales($date) {
        $query = "SELECT SUM(total_amount) as total FROM sales WHERE DATE(sale_date) = ?";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $date);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row['total'] ?? 0;
    }
    
    public function getMonthlySales($year, $month) {
        $query = "SELECT SUM(total_amount) as total 
                  FROM sales 
                  WHERE YEAR(sale_date) = ? AND MONTH(sale_date) = ?";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $year);
        $stmt->bindParam(2, $month);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row['total'] ?? 0;
    }
    
    public function getYearlySales($year) {
        $query = "SELECT SUM(total_amount) as total FROM sales WHERE YEAR(sale_date) = ?";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $year);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row['total'] ?? 0;
    }
    
    public function getSalesByDateRange($start_date, $end_date) {
        $query = "SELECT s.*, c.name as customer_name, u.full_name as cashier_name 
                  FROM sales s 
                  LEFT JOIN customers c ON s.customer_id = c.id 
                  LEFT JOIN users u ON s.user_id = u.id 
                  WHERE DATE(s.sale_date) BETWEEN ? AND ? 
                  ORDER BY s.sale_date DESC";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $start_date);
        $stmt->bindParam(2, $end_date);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_OBJ);
    }
    
    public function getBestSellingProducts($limit = 5, $start_date = null, $end_date = null) {
        $query = "SELECT p.name, SUM(si.quantity) as total_quantity, SUM(si.total) as total_sales 
                  FROM sale_items si 
                  JOIN products p ON si.product_id = p.id 
                  JOIN sales s ON si.sale_id = s.id";
        
        if($start_date && $end_date) {
            $query .= " WHERE DATE(s.sale_date) BETWEEN ? AND ?";
        }
        
        $query .= " GROUP BY p.name ORDER BY total_quantity DESC LIMIT ?";
        
        $stmt = $this->conn->prepare($query);
        
        if($start_date && $end_date) {
            $stmt->bindParam(1, $start_date);
            $stmt->bindParam(2, $end_date);
            $stmt->bindParam(3, $limit, PDO::PARAM_INT);
        } else {
            $stmt->bindParam(1, $limit, PDO::PARAM_INT);
        }
        
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_OBJ);
    }
    
    public function getSalesByPaymentType($start_date, $end_date) {
        $query = "SELECT payment_type, COUNT(*) as count, SUM(total_amount) as total 
                  FROM sales 
                  WHERE DATE(sale_date) BETWEEN ? AND ? 
                  GROUP BY payment_type";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $start_date);
        $stmt->bindParam(2, $end_date);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_OBJ);
    }
}
?>