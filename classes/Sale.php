<?php
class Sale {
    private $conn;
    private $table = 'sales';
    
    public $id;
    public $user_id;
    public $customer_id;
    public $total_amount;
    public $amount_received;
    public $change_amount;
    public $discount;
    public $payment_type;
    public $sale_date;
    public $created_at;
    
    public function __construct($db) {
        $this->conn = $db;
    }
    
    public function read($limit = null) {
        $query = "SELECT s.*, c.name as customer_name, u.full_name as cashier_name 
                  FROM " . $this->table . " s 
                  LEFT JOIN customers c ON s.customer_id = c.id 
                  LEFT JOIN users u ON s.user_id = u.id 
                  ORDER BY s.sale_date DESC";
        
        if($limit) {
            $query .= " LIMIT " . intval($limit);
        }
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_OBJ);
    }
    
    public function readOne() {
        $query = "SELECT s.*, c.name as customer_name, u.full_name as cashier_name 
                  FROM " . $this->table . " s 
                  LEFT JOIN customers c ON s.customer_id = c.id 
                  LEFT JOIN users u ON s.user_id = u.id 
                  WHERE s.id = ? LIMIT 1";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $this->id);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_OBJ);
    }
    
    public function create($cart_items) {
        try {
            $this->conn->beginTransaction();
            
            // Insert sale record
            $query = "INSERT INTO " . $this->table . " 
                      SET user_id = :user_id, 
                          customer_id = :customer_id, 
                          total_amount = :total_amount, 
                          amount_received = :amount_received, 
                          change_amount = :change_amount, 
                          discount = :discount, 
                          payment_type = :payment_type, 
                          sale_date = NOW()";
            
            $stmt = $this->conn->prepare($query);
            
            $stmt->bindParam(':user_id', $this->user_id);
            $stmt->bindParam(':customer_id', $this->customer_id);
            $stmt->bindParam(':total_amount', $this->total_amount);
            $stmt->bindParam(':amount_received', $this->amount_received);
            $stmt->bindParam(':change_amount', $this->change_amount);
            $stmt->bindParam(':discount', $this->discount);
            $stmt->bindParam(':payment_type', $this->payment_type);
            
            if($stmt->execute()) {
                $sale_id = $this->conn->lastInsertId();
                
                // Insert sale items
                $item_query = "INSERT INTO sale_items 
                               SET sale_id = :sale_id, 
                                   product_id = :product_id, 
                                   quantity = :quantity, 
                                   price = :price, 
                                   total = :total";
                
                $item_stmt = $this->conn->prepare($item_query);
                
                // Update product stock
                $stock_query = "UPDATE products SET stock = stock - :quantity WHERE id = :product_id";
                $stock_stmt = $this->conn->prepare($stock_query);
                
                foreach($cart_items as $item) {
                    // Insert sale item
                    $item_stmt->bindParam(':sale_id', $sale_id);
                    $item_stmt->bindParam(':product_id', $item['product_id']);
                    $item_stmt->bindParam(':quantity', $item['quantity']);
                    $item_stmt->bindParam(':price', $item['price']);
                    $item_stmt->bindParam(':total', $item['total']);
                    $item_stmt->execute();
                    
                    // Update stock
                    $stock_stmt->bindParam(':quantity', $item['quantity']);
                    $stock_stmt->bindParam(':product_id', $item['product_id']);
                    $stock_stmt->execute();
                }
                
                $this->conn->commit();
                return $sale_id;
            }
            
            $this->conn->rollback();
            return false;
            
        } catch(Exception $e) {
            $this->conn->rollback();
            return false;
        }
    }
    
    public function getSaleItems() {
        $query = "SELECT si.*, p.name as product_name 
                  FROM sale_items si 
                  JOIN products p ON si.product_id = p.id 
                  WHERE si.sale_id = ? 
                  ORDER BY si.id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $this->id);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_OBJ);
    }
    
    public function delete() {
        try {
            $this->conn->beginTransaction();
            
            // Get sale items to restore stock
            $items = $this->getSaleItems();
            
            // Restore product stock
            $stock_query = "UPDATE products SET stock = stock + :quantity WHERE id = :product_id";
            $stock_stmt = $this->conn->prepare($stock_query);
            
            foreach($items as $item) {
                $stock_stmt->bindParam(':quantity', $item->quantity);
                $stock_stmt->bindParam(':product_id', $item->product_id);
                $stock_stmt->execute();
            }
            
            // Delete sale items
            $delete_items_query = "DELETE FROM sale_items WHERE sale_id = ?";
            $delete_items_stmt = $this->conn->prepare($delete_items_query);
            $delete_items_stmt->bindParam(1, $this->id);
            $delete_items_stmt->execute();
            
            // Delete sale
            $delete_sale_query = "DELETE FROM " . $this->table . " WHERE id = ?";
            $delete_sale_stmt = $this->conn->prepare($delete_sale_query);
            $delete_sale_stmt->bindParam(1, $this->id);
            $delete_sale_stmt->execute();
            
            $this->conn->commit();
            return true;
            
        } catch(Exception $e) {
            $this->conn->rollback();
            return false;
        }
    }
    
    public function count() {
        $query = "SELECT COUNT(*) as total FROM " . $this->table;
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row['total'];
    }
    
    public function getTodaysSales() {
        $query = "SELECT COUNT(*) as count, SUM(total_amount) as total 
                  FROM " . $this->table . " 
                  WHERE DATE(sale_date) = CURDATE()";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_OBJ);
    }
    
    public function searchByDateRange($start_date, $end_date) {
        $query = "SELECT s.*, c.name as customer_name, u.full_name as cashier_name 
                  FROM " . $this->table . " s 
                  LEFT JOIN customers c ON s.customer_id = c.id 
                  LEFT JOIN users u ON s.user_id = u.id 
                  WHERE DATE(s.sale_date) BETWEEN ? AND ? 
                  ORDER BY s.sale_date DESC";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $start_date);
        $stmt->bindParam(2, $end_date);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_OBJ);
    }
}
?>
