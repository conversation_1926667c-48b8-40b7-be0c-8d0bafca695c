/* POS System Specific Styles */

/* Product Grid */
.product-grid {
    max-height: 600px;
    overflow-y: auto;
}

.product-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    min-height: 120px;
}

.product-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    border-color: #007bff;
}

.product-card.selected {
    border-color: #28a745;
    background-color: #f8fff9;
}

.product-card .card-title {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
}

.product-card .card-text {
    font-size: 0.8rem;
    margin-bottom: 0.25rem;
}

.product-card .price {
    font-size: 1rem;
    font-weight: 700;
    color: #28a745;
}

.product-card .stock-low {
    color: #dc3545 !important;
    font-weight: 600;
}

/* Cart Styles */
.cart-container {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    max-height: 600px;
    display: flex;
    flex-direction: column;
}

.cart-header {
    background-color: #007bff;
    color: white;
    padding: 1rem;
    border-radius: 10px 10px 0 0;
    font-weight: 600;
}

.cart-items {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    max-height: 300px;
}

.cart-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #eee;
}

.cart-item:last-child {
    border-bottom: none;
}

.cart-item-info {
    flex: 1;
}

.cart-item-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.25rem;
}

.cart-item-price {
    color: #666;
    font-size: 0.9rem;
}

.cart-item-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quantity-control {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.quantity-btn {
    width: 30px;
    height: 30px;
    border: none;
    border-radius: 50%;
    background-color: #f8f9fa;
    color: #333;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
}

.quantity-btn:hover {
    background-color: #007bff;
    color: white;
}

.quantity-input {
    width: 50px;
    text-align: center;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 0.25rem;
}

.remove-item {
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 0.25rem 0.5rem;
    cursor: pointer;
    transition: all 0.2s;
}

.remove-item:hover {
    background-color: #c82333;
}

/* Cart Summary */
.cart-summary {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 0 0 10px 10px;
    border-top: 1px solid #dee2e6;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.summary-row.total {
    font-weight: 700;
    font-size: 1.2rem;
    color: #333;
    border-top: 2px solid #dee2e6;
    padding-top: 0.5rem;
    margin-top: 0.5rem;
}

/* Payment Section */
.payment-section {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    margin-top: 1rem;
}

.payment-methods {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.payment-method {
    flex: 1;
    padding: 0.75rem;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    background-color: #fff;
    cursor: pointer;
    text-align: center;
    transition: all 0.2s;
}

.payment-method:hover {
    border-color: #007bff;
}

.payment-method.active {
    border-color: #28a745;
    background-color: #f8fff9;
    color: #28a745;
}

.payment-method i {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.amount-input {
    font-size: 1.2rem;
    font-weight: 600;
    text-align: center;
    padding: 0.75rem;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.amount-input:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.change-display {
    background-color: #d4edda;
    color: #155724;
    padding: 1rem;
    border-radius: 8px;
    text-align: center;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.change-display.insufficient {
    background-color: #f8d7da;
    color: #721c24;
}

/* Complete Sale Button */
.complete-sale-btn {
    width: 100%;
    padding: 1rem;
    font-size: 1.2rem;
    font-weight: 700;
    background-color: #28a745;
    border: none;
    border-radius: 8px;
    color: white;
    cursor: pointer;
    transition: all 0.2s;
}

.complete-sale-btn:hover:not(:disabled) {
    background-color: #218838;
    transform: translateY(-1px);
}

.complete-sale-btn:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
}

/* Search and Filter */
.search-filter-section {
    background-color: #fff;
    padding: 1rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 1rem;
}

.search-input {
    border-radius: 25px;
    padding: 0.75rem 1rem;
    border: 2px solid #dee2e6;
    transition: all 0.2s;
}

.search-input:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.category-filter {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-top: 1rem;
}

.category-btn {
    padding: 0.5rem 1rem;
    border: 2px solid #dee2e6;
    border-radius: 20px;
    background-color: #fff;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 0.9rem;
}

.category-btn:hover {
    border-color: #007bff;
    color: #007bff;
}

.category-btn.active {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
}

/* Responsive Design */
@media (max-width: 991.98px) {
    .cart-container {
        margin-top: 2rem;
    }
    
    .payment-methods {
        flex-direction: column;
    }
    
    .category-filter {
        justify-content: center;
    }
}

@media (max-width: 767.98px) {
    .product-card {
        min-height: 100px;
    }
    
    .product-card .card-title {
        font-size: 0.8rem;
    }
    
    .cart-items {
        max-height: 250px;
    }
    
    .cart-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .cart-item-controls {
        width: 100%;
        justify-content: space-between;
    }
}

/* Animation for cart updates */
.cart-item-enter {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.cart-item-remove {
    animation: slideOutRight 0.3s ease-in;
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}
