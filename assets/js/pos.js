// POS System JavaScript
let cart = [];
let currentCustomer = null;

document.addEventListener('DOMContentLoaded', function() {
    // Initialize POS system
    initializePOS();

    // Product selection
    document.querySelectorAll('.product-card').forEach(card => {
        card.addEventListener('click', function() {
            addToCart(this);
        });
    });

    // Payment method selection
    document.querySelectorAll('input[name="payment_method"]').forEach(radio => {
        radio.addEventListener('change', updatePaymentMethod);
    });

    // Amount received input
    const amountReceivedInput = document.getElementById('amount_received');
    if (amountReceivedInput) {
        amountReceivedInput.addEventListener('input', calculateChange);
    }

    // Complete sale button
    const completeSaleBtn = document.getElementById('complete_sale');
    if (completeSaleBtn) {
        completeSaleBtn.addEventListener('click', completeSale);
    }

    // Clear cart button
    const clearCartBtn = document.getElementById('clear_cart');
    if (clearCartBtn) {
        clearCartBtn.addEventListener('click', clearCart);
    }

    // Customer search
    const customerSearch = document.getElementById('customer_search');
    if (customerSearch) {
        customerSearch.addEventListener('input', searchCustomers);
    }
});

function initializePOS() {
    updateCartDisplay();
    updateTotals();

    // Focus on barcode input if available
    const barcodeInput = document.getElementById('barcode_input');
    if (barcodeInput) {
        barcodeInput.focus();
        barcodeInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                searchProductByBarcode(this.value);
                this.value = '';
            }
        });
    }
}

function addToCart(productCard) {
    const productId = parseInt(productCard.dataset.productId);
    const productName = productCard.dataset.productName;
    const productPrice = parseFloat(productCard.dataset.productPrice);
    const productStock = parseInt(productCard.dataset.productStock);

    // Check if product already in cart
    const existingItem = cart.find(item => item.id === productId);

    if (existingItem) {
        if (existingItem.quantity < productStock) {
            existingItem.quantity++;
            existingItem.total = existingItem.quantity * existingItem.price;
        } else {
            showNotification('Insufficient stock available', 'warning');
            return;
        }
    } else {
        if (productStock > 0) {
            cart.push({
                id: productId,
                name: productName,
                price: productPrice,
                quantity: 1,
                total: productPrice,
                stock: productStock
            });
        } else {
            showNotification('Product out of stock', 'warning');
            return;
        }
    }

    // Visual feedback
    productCard.classList.add('selected');
    setTimeout(() => {
        productCard.classList.remove('selected');
    }, 200);

    updateCartDisplay();
    updateTotals();
    showNotification(`${productName} added to cart`, 'success');
}

function removeFromCart(productId) {
    cart = cart.filter(item => item.id !== productId);
    updateCartDisplay();
    updateTotals();
    showNotification('Item removed from cart', 'info');
}

function updateQuantity(productId, newQuantity) {
    const item = cart.find(item => item.id === productId);
    if (item) {
        if (newQuantity <= 0) {
            removeFromCart(productId);
            return;
        }

        if (newQuantity > item.stock) {
            showNotification('Quantity exceeds available stock', 'warning');
            return;
        }

        item.quantity = newQuantity;
        item.total = item.quantity * item.price;
        updateCartDisplay();
        updateTotals();
    }
}

function updateCartDisplay() {
    const cartItems = document.getElementById('cart_items');
    if (!cartItems) return;

    if (cart.length === 0) {
        cartItems.innerHTML = '<div class="text-center text-muted py-4">Cart is empty</div>';
        return;
    }

    let html = '';
    cart.forEach(item => {
        html += `
            <div class="cart-item" data-product-id="${item.id}">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="flex-grow-1">
                        <h6 class="mb-1">${item.name}</h6>
                        <small class="text-muted">$${item.price.toFixed(2)} each</small>
                    </div>
                    <div class="quantity-controls">
                        <button class="btn btn-sm btn-outline-secondary" onclick="updateQuantity(${item.id}, ${item.quantity - 1})">-</button>
                        <span class="mx-2">${item.quantity}</span>
                        <button class="btn btn-sm btn-outline-secondary" onclick="updateQuantity(${item.id}, ${item.quantity + 1})">+</button>
                    </div>
                    <div class="text-end ms-3">
                        <div class="fw-bold">$${item.total.toFixed(2)}</div>
                        <button class="btn btn-sm btn-outline-danger" onclick="removeFromCart(${item.id})">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
    });

    cartItems.innerHTML = html;
}

function updateTotals() {
    const subtotal = cart.reduce((sum, item) => sum + item.total, 0);
    const discount = parseFloat(document.getElementById('discount')?.value || 0);
    const total = subtotal - discount;

    // Update display
    const subtotalElement = document.getElementById('subtotal');
    const totalElement = document.getElementById('total');

    if (subtotalElement) subtotalElement.textContent = `$${subtotal.toFixed(2)}`;
    if (totalElement) totalElement.textContent = `$${total.toFixed(2)}`;

    // Update hidden input for form submission
    const totalInput = document.getElementById('total_amount');
    if (totalInput) totalInput.value = total.toFixed(2);

    calculateChange();
}

function calculateChange() {
    const total = parseFloat(document.getElementById('total_amount')?.value || 0);
    const amountReceived = parseFloat(document.getElementById('amount_received')?.value || 0);
    const change = amountReceived - total;

    const changeElement = document.getElementById('change');
    if (changeElement) {
        changeElement.textContent = `$${Math.max(0, change).toFixed(2)}`;

        // Update change input for form submission
        const changeInput = document.getElementById('change_amount');
        if (changeInput) changeInput.value = Math.max(0, change).toFixed(2);
    }

    // Enable/disable complete sale button
    const completeSaleBtn = document.getElementById('complete_sale');
    if (completeSaleBtn) {
        completeSaleBtn.disabled = cart.length === 0 || amountReceived < total;
    }
}

function updatePaymentMethod() {
    const selectedMethod = document.querySelector('input[name="payment_method"]:checked')?.value;
    const amountReceivedGroup = document.getElementById('amount_received_group');

    if (selectedMethod === 'card' || selectedMethod === 'mobile') {
        // For card/mobile payments, set amount received to exact total
        const total = parseFloat(document.getElementById('total_amount')?.value || 0);
        const amountReceivedInput = document.getElementById('amount_received');
        if (amountReceivedInput) {
            amountReceivedInput.value = total.toFixed(2);
            amountReceivedInput.readOnly = true;
        }
        if (amountReceivedGroup) {
            amountReceivedGroup.style.display = 'none';
        }
    } else {
        // For cash payments, allow manual input
        const amountReceivedInput = document.getElementById('amount_received');
        if (amountReceivedInput) {
            amountReceivedInput.readOnly = false;
        }
        if (amountReceivedGroup) {
            amountReceivedGroup.style.display = 'block';
        }
    }

    calculateChange();
}

function searchProductByBarcode(barcode) {
    if (!barcode) return;

    const productCard = document.querySelector(`[data-barcode="${barcode}"]`);
    if (productCard) {
        addToCart(productCard);
        // Scroll to product
        productCard.scrollIntoView({ behavior: 'smooth', block: 'center' });
    } else {
        showNotification('Product not found with barcode: ' + barcode, 'warning');
    }
}

function clearCart() {
    if (cart.length === 0) return;

    if (confirm('Are you sure you want to clear the cart?')) {
        cart = [];
        updateCartDisplay();
        updateTotals();
        showNotification('Cart cleared', 'info');
    }
}

function completeSale() {
    if (cart.length === 0) {
        showNotification('Cart is empty', 'warning');
        return;
    }

    const total = parseFloat(document.getElementById('total_amount')?.value || 0);
    const amountReceived = parseFloat(document.getElementById('amount_received')?.value || 0);

    if (amountReceived < total) {
        showNotification('Insufficient amount received', 'warning');
        return;
    }

    // Prepare cart data for submission
    const cartData = document.getElementById('cart_data');
    if (cartData) {
        cartData.value = JSON.stringify(cart);
    }

    // Submit the form
    const saleForm = document.getElementById('sale_form');
    if (saleForm) {
        saleForm.submit();
    }
}

// Utility function for notifications
function showNotification(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    // Auto-remove after 3 seconds
    setTimeout(function() {
        if (alertDiv.parentNode) {
            const bsAlert = new bootstrap.Alert(alertDiv);
            bsAlert.close();
        }
    }, 3000);
}

// Quick amount buttons
function setQuickAmount(amount) {
    const amountReceivedInput = document.getElementById('amount_received');
    if (amountReceivedInput && !amountReceivedInput.readOnly) {
        amountReceivedInput.value = amount.toFixed(2);
        calculateChange();
    }
}