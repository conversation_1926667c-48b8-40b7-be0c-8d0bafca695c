document.addEventListener('DOMContentLoaded', function() {
    const cart = [];
    let subtotal = 0;
    let total = 0;
    let discount = 0;
    
    // Add to cart functionality
    document.querySelectorAll('.add-to-cart').forEach(button => {
        button.addEventListener('click', function() {
            const productItem = this.closest('.product-item');
            const productId = parseInt(productItem.dataset.id);
            const productName = productItem.dataset.name;
            const productPrice = parseFloat(productItem.dataset.price);
            const productStock = parseInt(productItem.dataset.stock);
            
            // Check if product already in cart
            const existingItem = cart.find(item => item.id === productId);
            
            if(existingItem) {
                if(existingItem.quantity < productStock) {
                    existingItem.quantity += 1;
                    existingItem.total = existingItem.quantity * existingItem.price;
                } else {
                    alert('Cannot add more than available stock');
                }
            } else {
                if(productStock > 0) {
                    cart.push({
                        id: productId,
                        name: productName,
                        price: productPrice,
                        quantity: 1,
                        total: productPrice
                    });
                } else {
                    alert('Product out of stock');
                }
            }
            
            updateCart();
        });
    });
    
    // Update cart display
    function updateCart() {
        const cartItemsEl = document.getElementById('cartItems');
        const subtotalEl = document.getElementById('subtotal');
        const totalEl = document.getElementById('totalAmount');
        const cartItemsInput = document.getElementById('cart_items');
        const totalAmountInput = document.getElementById('total_amount');
        
        // Clear cart items
        cartItemsEl.innerHTML = '';
        
        // Calculate subtotal
        subtotal = cart.reduce((sum, item) => sum + item.total, 0);
        discount = parseFloat(document.getElementById('discount').value) || 0;
        total = subtotal - discount;
        
        // Update cart items
        cart.forEach(item => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${item.name}</td>
                <td>
                    <input type="number" class="form-control form-control-sm qty-input" 
                           data-id="${item.id}" value="${item.quantity}" min="1" max="${item.stock}">
                </td>
                <td>$${item.price.toFixed(2)}</td>
                <td>$${item.total.toFixed(2)}</td>
                <td>
                    <button class="btn btn-sm btn-danger remove-item" data-id="${item.id}">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            `;
            cartItemsEl.appendChild(row);
        });
        
        // Update totals
        subtotalEl.textContent = `$${subtotal.toFixed(2)}`;
        totalEl.textContent = `$${total.toFixed(2)}`;
        
        // Update hidden inputs
        cartItemsInput.value = JSON.stringify(cart);
        totalAmountInput.value = total;
        
        // Update change amount when amount received changes
        updateChangeAmount();
        
        // Add event listeners to quantity inputs
        document.querySelectorAll('.qty-input').forEach(input => {
            input.addEventListener('change', function() {
                const productId = parseInt(this.dataset.id);
                const newQty = parseInt(this.value);
                const cartItem = cart.find(item => item.id === productId);
                
                if(cartItem && newQty > 0) {
                    cartItem.quantity = newQty;
                    cartItem.total = cartItem.quantity * cartItem.price;
                    updateCart();
                }
            });
        });
        
        // Add event listeners to remove buttons
        document.querySelectorAll('.remove-item').forEach(button => {
            button.addEventListener('click', function() {
                const productId = parseInt(this.dataset.id);
                const index = cart.findIndex(item => item.id === productId);
                
                if(index !== -1) {
                    cart.splice(index, 1);
                    updateCart();
                }
            });
        });
    }
    
    // Update change amount
    function updateChangeAmount() {
        const amountReceived = parseFloat(document.getElementById('amount_received').value) || 0;
        const changeAmount = amountReceived - total;
        document.getElementById('change_amount').value = changeAmount.toFixed(2);
    }
    
    // Event listeners for amount received and discount
    document.getElementById('amount_received').addEventListener('input', updateChangeAmount);
    document.getElementById('discount').addEventListener('input', function() {
        discount = parseFloat(this.value) || 0;
        total = subtotal - discount;
        document.getElementById('totalAmount').textContent = `$${total.toFixed(2)}`;
        document.getElementById('total_amount').value = total;
        updateChangeAmount();
    });
    
    // Product search functionality
    document.getElementById('productSearch').addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        document.querySelectorAll('.product-item').forEach(item => {
            const productName = item.dataset.name.toLowerCase();
            if(productName.includes(searchTerm)) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    });
    
    // Category filter functionality
    document.getElementById('categoryFilter').addEventListener('change', function() {
        const categoryId = this.value;
        document.querySelectorAll('.product-item').forEach(item => {
            if(categoryId === '' || item.dataset.category === categoryId) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    });
});