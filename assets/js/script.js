// Main JavaScript file for Sales Management System

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        var alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
        alerts.forEach(function(alert) {
            var bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);

    // Confirm delete actions
    var deleteButtons = document.querySelectorAll('[data-confirm-delete]');
    deleteButtons.forEach(function(button) {
        button.addEventListener('click', function(e) {
            var message = this.getAttribute('data-confirm-delete') || 'Are you sure you want to delete this item?';
            if (!confirm(message)) {
                e.preventDefault();
                return false;
            }
        });
    });

    // Form validation
    var forms = document.querySelectorAll('.needs-validation');
    forms.forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });

    // Search functionality
    var searchInputs = document.querySelectorAll('[data-search-target]');
    searchInputs.forEach(function(input) {
        var targetSelector = input.getAttribute('data-search-target');
        var searchDelay;
        
        input.addEventListener('input', function() {
            clearTimeout(searchDelay);
            var searchTerm = this.value.toLowerCase();
            
            searchDelay = setTimeout(function() {
                var targets = document.querySelectorAll(targetSelector);
                targets.forEach(function(target) {
                    var text = target.textContent.toLowerCase();
                    if (text.includes(searchTerm)) {
                        target.style.display = '';
                    } else {
                        target.style.display = 'none';
                    }
                });
            }, 300);
        });
    });

    // Number formatting
    var numberInputs = document.querySelectorAll('input[type="number"][data-format="currency"]');
    numberInputs.forEach(function(input) {
        input.addEventListener('blur', function() {
            var value = parseFloat(this.value);
            if (!isNaN(value)) {
                this.value = value.toFixed(2);
            }
        });
    });

    // Auto-calculate totals
    var quantityInputs = document.querySelectorAll('input[data-calculate="total"]');
    quantityInputs.forEach(function(input) {
        input.addEventListener('input', calculateRowTotal);
    });

    function calculateRowTotal() {
        var row = this.closest('tr');
        if (!row) return;

        var quantity = parseFloat(this.value) || 0;
        var priceInput = row.querySelector('input[data-price]');
        var totalInput = row.querySelector('input[data-total]');

        if (priceInput && totalInput) {
            var price = parseFloat(priceInput.value) || 0;
            var total = quantity * price;
            totalInput.value = total.toFixed(2);
            
            // Trigger change event for further calculations
            totalInput.dispatchEvent(new Event('change'));
        }
    }

    // Barcode scanner simulation (Enter key)
    var barcodeInput = document.querySelector('#barcodeInput');
    if (barcodeInput) {
        barcodeInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                searchProductByBarcode(this.value);
                this.value = '';
            }
        });
    }

    function searchProductByBarcode(barcode) {
        if (!barcode) return;

        // Find product by barcode
        var productCards = document.querySelectorAll('.product-card');
        var found = false;

        productCards.forEach(function(card) {
            var productBarcode = card.getAttribute('data-barcode');
            if (productBarcode === barcode) {
                card.click();
                found = true;
                // Scroll to product
                card.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        });

        if (!found) {
            showNotification('Product not found with barcode: ' + barcode, 'warning');
        }
    }

    // Notification system
    function showNotification(message, type = 'info') {
        var alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(alertDiv);

        // Auto-remove after 5 seconds
        setTimeout(function() {
            if (alertDiv.parentNode) {
                var bsAlert = new bootstrap.Alert(alertDiv);
                bsAlert.close();
            }
        }, 5000);
    }

    // Export notification function globally
    window.showNotification = showNotification;

    // Print functionality
    var printButtons = document.querySelectorAll('[data-print]');
    printButtons.forEach(function(button) {
        button.addEventListener('click', function() {
            var target = this.getAttribute('data-print');
            if (target) {
                var element = document.querySelector(target);
                if (element) {
                    printElement(element);
                }
            } else {
                window.print();
            }
        });
    });

    function printElement(element) {
        var printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head>
                    <title>Print</title>
                    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
                    <style>
                        body { font-family: Arial, sans-serif; }
                        @media print {
                            .no-print { display: none !important; }
                        }
                    </style>
                </head>
                <body>
                    ${element.outerHTML}
                </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.focus();
        printWindow.print();
        printWindow.close();
    }

    // Data tables enhancement
    var tables = document.querySelectorAll('.data-table');
    tables.forEach(function(table) {
        // Add sorting functionality
        var headers = table.querySelectorAll('th[data-sort]');
        headers.forEach(function(header) {
            header.style.cursor = 'pointer';
            header.addEventListener('click', function() {
                sortTable(table, this);
            });
        });
    });

    function sortTable(table, header) {
        var column = Array.from(header.parentNode.children).indexOf(header);
        var rows = Array.from(table.querySelectorAll('tbody tr'));
        var ascending = !header.classList.contains('sort-asc');

        rows.sort(function(a, b) {
            var aVal = a.children[column].textContent.trim();
            var bVal = b.children[column].textContent.trim();

            // Try to parse as numbers
            var aNum = parseFloat(aVal.replace(/[^0-9.-]/g, ''));
            var bNum = parseFloat(bVal.replace(/[^0-9.-]/g, ''));

            if (!isNaN(aNum) && !isNaN(bNum)) {
                return ascending ? aNum - bNum : bNum - aNum;
            }

            // String comparison
            return ascending ? aVal.localeCompare(bVal) : bVal.localeCompare(aVal);
        });

        // Update header classes
        table.querySelectorAll('th').forEach(function(th) {
            th.classList.remove('sort-asc', 'sort-desc');
        });
        header.classList.add(ascending ? 'sort-asc' : 'sort-desc');

        // Reorder rows
        var tbody = table.querySelector('tbody');
        rows.forEach(function(row) {
            tbody.appendChild(row);
        });
    }

    // Auto-save form data
    var autoSaveForms = document.querySelectorAll('[data-auto-save]');
    autoSaveForms.forEach(function(form) {
        var formId = form.getAttribute('data-auto-save');
        
        // Load saved data
        loadFormData(form, formId);
        
        // Save on input
        form.addEventListener('input', function() {
            saveFormData(form, formId);
        });
    });

    function saveFormData(form, formId) {
        var formData = new FormData(form);
        var data = {};
        
        for (var [key, value] of formData.entries()) {
            data[key] = value;
        }
        
        localStorage.setItem('form_' + formId, JSON.stringify(data));
    }

    function loadFormData(form, formId) {
        var savedData = localStorage.getItem('form_' + formId);
        if (savedData) {
            try {
                var data = JSON.parse(savedData);
                for (var key in data) {
                    var input = form.querySelector(`[name="${key}"]`);
                    if (input) {
                        input.value = data[key];
                    }
                }
            } catch (e) {
                console.error('Error loading form data:', e);
            }
        }
    }

    // Clear auto-saved data on successful form submission
    document.addEventListener('submit', function(e) {
        var form = e.target;
        var formId = form.getAttribute('data-auto-save');
        if (formId) {
            setTimeout(function() {
                localStorage.removeItem('form_' + formId);
            }, 1000);
        }
    });
});

// Utility functions
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
}

function formatDate(date) {
    return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    }).format(new Date(date));
}

function formatDateTime(date) {
    return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    }).format(new Date(date));
}
