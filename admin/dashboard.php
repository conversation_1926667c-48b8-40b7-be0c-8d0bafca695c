<?php
require_once '../../includes/auth.php';

if(!isAdmin()) {
    header('Location: ../cashier/pos.php');
    exit;
}

// Include necessary classes
require_once '../../classes/Product.php';
require_once '../../classes/Customer.php';
require_once '../../classes/Sale.php';
require_once '../../classes/Report.php';

$database = new Database();
$db = $database->getConnection();

$product = new Product($db);
$customer = new Customer($db);
$sale = new Sale($db);
$report = new Report($db);

// Get counts for dashboard
$product_count = $product->count();
$customer_count = $customer->count();
$today_sales = $report->getDailySales(date('Y-m-d'));
$low_stock_products = $product->getLowStock();

// Get recent sales
$recent_sales = $sale->read(5);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Sales Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link href="../../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include '../../includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include '../../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Dashboard</h1>
                </div>
                
                <!-- Summary Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-white bg-primary mb-3">
                            <div class="card-body">
                                <h5 class="card-title">Products</h5>
                                <p class="card-text display-6"><?php echo $product_count; ?></p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-white bg-success mb-3">
                            <div class="card-body">
                                <h5 class="card-title">Customers</h5>
                                <p class="card-text display-6"><?php echo $customer_count; ?></p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-white bg-info mb-3">
                            <div class="card-body">
                                <h5 class="card-title">Today's Sales</h5>
                                <p class="card-text display-6">$<?php echo number_format($today_sales, 2); ?></p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-white bg-warning mb-3">
                            <div class="card-body">
                                <h5 class="card-title">Low Stock</h5>
                                <p class="card-text display-6"><?php echo count($low_stock_products); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Sales and Low Stock -->
                <div class="row">
                    <div class="col-md-8">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5>Recent Sales</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-sm">
                                        <thead>
                                            <tr>
                                                <th>#</th>
                                                <th>Date</th>
                                                <th>Customer</th>
                                                <th>Amount</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach($recent_sales as $sale): ?>
                                            <tr>
                                                <td><?php echo $sale->id; ?></td>
                                                <td><?php echo date('M j, Y', strtotime($sale->sale_date)); ?></td>
                                                <td><?php echo $sale->customer_name ?? 'Walk-in'; ?></td>
                                                <td>$<?php echo number_format($sale->total_amount, 2); ?></td>
                                                <td>
                                                    <a href="sales/view.php?id=<?php echo $sale->id; ?>" class="btn btn-sm btn-info">View</a>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5>Low Stock Products</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Product</th>
                                                <th>Stock</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach($low_stock_products as $product): ?>
                                            <tr>
                                                <td><?php echo $product->name; ?></td>
                                                <td class="<?php echo $product->stock < $product->min_stock ? 'text-danger' : 'text-warning'; ?>">
                                                    <?php echo $product->stock; ?>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../../assets/js/script.js"></script>
</body>
</html>