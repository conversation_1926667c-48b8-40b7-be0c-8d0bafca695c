<?php
require_once '../../includes/auth.php';
if(!isAdmin()) {
    header('Location: ../../cashier/pos.php');
    exit;
}

require_once '../../classes/Product.php';
require_once '../../classes/Category.php';

$database = new Database();
$db = $database->getConnection();

$product = new Product($db);
$category = new Category($db);

$categories = $category->read();

// Handle form submission
if($_SERVER['REQUEST_METHOD'] == 'POST') {
    $product->name = $_POST['name'];
    $product->description = $_POST['description'];
    $product->barcode = $_POST['barcode'];
    $product->category_id = $_POST['category_id'] ?: null;
    $product->price = $_POST['price'];
    $product->cost = $_POST['cost'];
    $product->stock = $_POST['stock'];
    $product->min_stock = $_POST['min_stock'];
    
    if($product->create()) {
        $_SESSION['message'] = "Product added successfully";
        header("Location: index.php");
        exit();
    } else {
        $error = "Failed to add product. Please try again.";
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Product - Sales Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link href="../../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include '../../includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include '../../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Add Product</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="index.php" class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> Back to Products
                        </a>
                    </div>
                </div>
                
                <?php if(isset($error)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $error; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5>Product Information</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" class="needs-validation" novalidate>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="name" class="form-label">Product Name *</label>
                                            <input type="text" class="form-control" id="name" name="name" required>
                                            <div class="invalid-feedback">
                                                Please provide a product name.
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="barcode" class="form-label">Barcode</label>
                                            <input type="text" class="form-control" id="barcode" name="barcode">
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="description" class="form-label">Description</label>
                                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="category_id" class="form-label">Category</label>
                                            <select class="form-select" id="category_id" name="category_id">
                                                <option value="">Select Category</option>
                                                <?php foreach($categories as $cat): ?>
                                                    <option value="<?php echo $cat->id; ?>"><?php echo $cat->name; ?></option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="stock" class="form-label">Initial Stock *</label>
                                            <input type="number" class="form-control" id="stock" name="stock" min="0" required>
                                            <div class="invalid-feedback">
                                                Please provide initial stock quantity.
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <label for="cost" class="form-label">Cost Price *</label>
                                            <div class="input-group">
                                                <span class="input-group-text">$</span>
                                                <input type="number" class="form-control" id="cost" name="cost" step="0.01" min="0" required>
                                                <div class="invalid-feedback">
                                                    Please provide cost price.
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="price" class="form-label">Selling Price *</label>
                                            <div class="input-group">
                                                <span class="input-group-text">$</span>
                                                <input type="number" class="form-control" id="price" name="price" step="0.01" min="0" required>
                                                <div class="invalid-feedback">
                                                    Please provide selling price.
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="min_stock" class="form-label">Minimum Stock *</label>
                                            <input type="number" class="form-control" id="min_stock" name="min_stock" min="0" required>
                                            <div class="invalid-feedback">
                                                Please provide minimum stock level.
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                        <a href="index.php" class="btn btn-secondary me-md-2">Cancel</a>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-plus-circle"></i> Add Product
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5>Tips</h5>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <i class="bi bi-lightbulb text-warning"></i>
                                        <strong>Barcode:</strong> Leave empty to auto-generate
                                    </li>
                                    <li class="mb-2">
                                        <i class="bi bi-lightbulb text-warning"></i>
                                        <strong>Category:</strong> Create categories first for better organization
                                    </li>
                                    <li class="mb-2">
                                        <i class="bi bi-lightbulb text-warning"></i>
                                        <strong>Minimum Stock:</strong> Set alerts for low inventory
                                    </li>
                                    <li class="mb-2">
                                        <i class="bi bi-lightbulb text-warning"></i>
                                        <strong>Pricing:</strong> Ensure selling price covers costs and profit
                                    </li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="card mt-3">
                            <div class="card-header">
                                <h5>Quick Actions</h5>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <a href="../categories/add.php" class="btn btn-outline-primary btn-sm">
                                        <i class="bi bi-plus"></i> Add Category
                                    </a>
                                    <a href="index.php" class="btn btn-outline-secondary btn-sm">
                                        <i class="bi bi-list"></i> View All Products
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../../assets/js/script.js"></script>
    <script>
        // Auto-generate barcode if empty
        document.getElementById('name').addEventListener('blur', function() {
            var barcodeField = document.getElementById('barcode');
            if (!barcodeField.value && this.value) {
                // Generate simple barcode based on product name
                var barcode = Date.now().toString().slice(-8);
                barcodeField.value = barcode;
            }
        });
        
        // Calculate profit margin
        document.getElementById('cost').addEventListener('input', calculateMargin);
        document.getElementById('price').addEventListener('input', calculateMargin);
        
        function calculateMargin() {
            var cost = parseFloat(document.getElementById('cost').value) || 0;
            var price = parseFloat(document.getElementById('price').value) || 0;
            
            if (cost > 0 && price > 0) {
                var margin = ((price - cost) / price * 100).toFixed(2);
                var marginInfo = document.getElementById('margin-info');
                if (!marginInfo) {
                    marginInfo = document.createElement('small');
                    marginInfo.id = 'margin-info';
                    marginInfo.className = 'text-muted';
                    document.getElementById('price').parentNode.appendChild(marginInfo);
                }
                marginInfo.textContent = `Profit margin: ${margin}%`;
            }
        }
    </script>
</body>
</html>
