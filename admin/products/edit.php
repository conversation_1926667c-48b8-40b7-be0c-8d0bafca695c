<?php
require_once '../../includes/auth.php';
if(!isAdmin()) {
    header('Location: ../../cashier/pos.php');
    exit;
}

require_once '../../classes/Product.php';
require_once '../../classes/Category.php';

$database = new Database();
$db = $database->getConnection();

$product = new Product($db);
$category = new Category($db);

$categories = $category->read();

// Get product ID
if(!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: index.php");
    exit();
}

$product->id = $_GET['id'];
$product_data = $product->readOne();

if(!$product_data) {
    $_SESSION['message'] = "Product not found";
    header("Location: index.php");
    exit();
}

// Handle form submission
if($_SERVER['REQUEST_METHOD'] == 'POST') {
    $product->name = $_POST['name'];
    $product->description = $_POST['description'];
    $product->barcode = $_POST['barcode'];
    $product->category_id = $_POST['category_id'] ?: null;
    $product->price = $_POST['price'];
    $product->cost = $_POST['cost'];
    $product->stock = $_POST['stock'];
    $product->min_stock = $_POST['min_stock'];
    
    if($product->update()) {
        $_SESSION['message'] = "Product updated successfully";
        header("Location: index.php");
        exit();
    } else {
        $error = "Failed to update product. Please try again.";
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Product - Sales Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link href="../../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include '../../includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include '../../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Edit Product</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="index.php" class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> Back to Products
                        </a>
                    </div>
                </div>
                
                <?php if(isset($error)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $error; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5>Product Information</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" class="needs-validation" novalidate>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="name" class="form-label">Product Name *</label>
                                            <input type="text" class="form-control" id="name" name="name" value="<?php echo htmlspecialchars($product_data->name); ?>" required>
                                            <div class="invalid-feedback">
                                                Please provide a product name.
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="barcode" class="form-label">Barcode</label>
                                            <input type="text" class="form-control" id="barcode" name="barcode" value="<?php echo htmlspecialchars($product_data->barcode); ?>">
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="description" class="form-label">Description</label>
                                        <textarea class="form-control" id="description" name="description" rows="3"><?php echo htmlspecialchars($product_data->description); ?></textarea>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="category_id" class="form-label">Category</label>
                                            <select class="form-select" id="category_id" name="category_id">
                                                <option value="">Select Category</option>
                                                <?php foreach($categories as $cat): ?>
                                                    <option value="<?php echo $cat->id; ?>" <?php echo ($cat->id == $product_data->category_id) ? 'selected' : ''; ?>>
                                                        <?php echo $cat->name; ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="stock" class="form-label">Current Stock *</label>
                                            <input type="number" class="form-control" id="stock" name="stock" min="0" value="<?php echo $product_data->stock; ?>" required>
                                            <div class="invalid-feedback">
                                                Please provide stock quantity.
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <label for="cost" class="form-label">Cost Price *</label>
                                            <div class="input-group">
                                                <span class="input-group-text">$</span>
                                                <input type="number" class="form-control" id="cost" name="cost" step="0.01" min="0" value="<?php echo $product_data->cost; ?>" required>
                                                <div class="invalid-feedback">
                                                    Please provide cost price.
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="price" class="form-label">Selling Price *</label>
                                            <div class="input-group">
                                                <span class="input-group-text">$</span>
                                                <input type="number" class="form-control" id="price" name="price" step="0.01" min="0" value="<?php echo $product_data->price; ?>" required>
                                                <div class="invalid-feedback">
                                                    Please provide selling price.
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="min_stock" class="form-label">Minimum Stock *</label>
                                            <input type="number" class="form-control" id="min_stock" name="min_stock" min="0" value="<?php echo $product_data->min_stock; ?>" required>
                                            <div class="invalid-feedback">
                                                Please provide minimum stock level.
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                        <a href="index.php" class="btn btn-secondary me-md-2">Cancel</a>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-check-circle"></i> Update Product
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5>Product Status</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">Stock Status</label>
                                    <div class="alert <?php echo ($product_data->stock <= $product_data->min_stock) ? 'alert-danger' : 'alert-success'; ?>">
                                        <?php if($product_data->stock <= $product_data->min_stock): ?>
                                            <i class="bi bi-exclamation-triangle"></i> Low Stock Warning
                                        <?php else: ?>
                                            <i class="bi bi-check-circle"></i> Stock Level OK
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Profit Margin</label>
                                    <?php 
                                    $margin = (($product_data->price - $product_data->cost) / $product_data->price) * 100;
                                    ?>
                                    <div class="alert alert-info">
                                        <i class="bi bi-calculator"></i> <?php echo number_format($margin, 2); ?>%
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Created</label>
                                    <p class="text-muted"><?php echo date('M d, Y', strtotime($product_data->created_at)); ?></p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card mt-3">
                            <div class="card-header">
                                <h5>Quick Actions</h5>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <a href="add.php" class="btn btn-outline-primary btn-sm">
                                        <i class="bi bi-plus"></i> Add New Product
                                    </a>
                                    <a href="index.php" class="btn btn-outline-secondary btn-sm">
                                        <i class="bi bi-list"></i> View All Products
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../../assets/js/script.js"></script>
    <script>
        // Calculate profit margin
        document.getElementById('cost').addEventListener('input', calculateMargin);
        document.getElementById('price').addEventListener('input', calculateMargin);
        
        function calculateMargin() {
            var cost = parseFloat(document.getElementById('cost').value) || 0;
            var price = parseFloat(document.getElementById('price').value) || 0;
            
            if (cost > 0 && price > 0) {
                var margin = ((price - cost) / price * 100).toFixed(2);
                var marginInfo = document.getElementById('margin-info');
                if (!marginInfo) {
                    marginInfo = document.createElement('small');
                    marginInfo.id = 'margin-info';
                    marginInfo.className = 'text-muted';
                    document.getElementById('price').parentNode.appendChild(marginInfo);
                }
                marginInfo.textContent = `Profit margin: ${margin}%`;
            }
        }
        
        // Initial calculation
        calculateMargin();
    </script>
</body>
</html>
