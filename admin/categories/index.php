<?php
require_once '../../includes/auth.php';
if(!isAdmin()) {
    header('Location: ../../cashier/pos.php');
    exit;
}

require_once '../../classes/Category.php';

$database = new Database();
$db = $database->getConnection();

$category = new Category($db);
$categories = $category->read();

// Handle delete
if(isset($_POST['delete'])) {
    $category->id = $_POST['id'];
    if($category->delete()) {
        $_SESSION['message'] = "Category deleted successfully";
        header("Location: index.php");
        exit();
    } else {
        $error = "Cannot delete category. It may have products associated with it.";
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Categories - Sales Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link href="../../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include '../../includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include '../../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Categories</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="add.php" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-plus"></i> Add Category
                        </a>
                    </div>
                </div>
                
                <?php if(isset($_SESSION['message'])): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $_SESSION['message']; unset($_SESSION['message']); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <?php if(isset($error)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $error; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <div class="card mb-4">
                    <div class="card-header">
                        <div class="row">
                            <div class="col-md-6">
                                <h5>Category List</h5>
                            </div>
                            <div class="col-md-6">
                                <form method="GET" class="float-end">
                                    <div class="input-group">
                                        <input type="text" name="search" class="form-control form-control-sm" placeholder="Search categories...">
                                        <button class="btn btn-sm btn-outline-secondary" type="submit">Search</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-sm">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Name</th>
                                        <th>Description</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach($categories as $cat): ?>
                                    <tr>
                                        <td><?php echo $cat->id; ?></td>
                                        <td><?php echo htmlspecialchars($cat->name); ?></td>
                                        <td><?php echo htmlspecialchars($cat->description ?? 'N/A'); ?></td>
                                        <td><?php echo date('M d, Y', strtotime($cat->created_at)); ?></td>
                                        <td>
                                            <a href="edit.php?id=<?php echo $cat->id; ?>" class="btn btn-sm btn-warning">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <form method="POST" style="display:inline;">
                                                <input type="hidden" name="id" value="<?php echo $cat->id; ?>">
                                                <button type="submit" name="delete" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure? This will also remove the category from all products.')">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../../assets/js/script.js"></script>
</body>
</html>
