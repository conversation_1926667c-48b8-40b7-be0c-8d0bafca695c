<?php
require_once '../../includes/auth.php';
if(!isAdmin()) {
    header('Location: ../../cashier/pos.php');
    exit;
}

require_once '../../classes/Sale.php';
require_once '../../classes/Product.php';

$database = new Database();
$db = $database->getConnection();

$sale = new Sale($db);
$product = new Product($db);

// Get date range
$start_date = $_GET['start_date'] ?? date('Y-m-01');
$end_date = $_GET['end_date'] ?? date('Y-m-d');

// Get sales data
$sales_data = $sale->getSalesReport($start_date, $end_date);
$total_sales = $sale->getTotalSales($start_date, $end_date);
$top_products = $product->getTopSellingProducts($start_date, $end_date);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reports - Sales Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link href="../../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include '../../includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include '../../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Sales Reports</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button class="btn btn-sm btn-outline-secondary" onclick="window.print()">
                            <i class="bi bi-printer"></i> Print Report
                        </button>
                    </div>
                </div>
                
                <!-- Date Range Filter -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-4">
                                <label for="start_date" class="form-label">Start Date</label>
                                <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo $start_date; ?>">
                            </div>
                            <div class="col-md-4">
                                <label for="end_date" class="form-label">End Date</label>
                                <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo $end_date; ?>">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">Generate Report</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Summary Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-white bg-primary">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="card-title">$<?php echo number_format($total_sales['total_revenue'] ?? 0, 2); ?></h4>
                                        <p class="card-text">Total Revenue</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-currency-dollar fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-white bg-success">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="card-title"><?php echo $total_sales['total_sales'] ?? 0; ?></h4>
                                        <p class="card-text">Total Sales</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-receipt fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-white bg-info">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="card-title"><?php echo $total_sales['total_items'] ?? 0; ?></h4>
                                        <p class="card-text">Items Sold</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-box fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-white bg-warning">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="card-title">$<?php echo number_format(($total_sales['total_revenue'] ?? 0) / max(1, $total_sales['total_sales'] ?? 1), 2); ?></h4>
                                        <p class="card-text">Avg. Sale</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-graph-up fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Sales Table -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Sales Details</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-sm">
                                <thead>
                                    <tr>
                                        <th>Sale ID</th>
                                        <th>Date</th>
                                        <th>Cashier</th>
                                        <th>Customer</th>
                                        <th>Items</th>
                                        <th>Total</th>
                                        <th>Payment</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if($sales_data): ?>
                                        <?php foreach($sales_data as $sale_item): ?>
                                        <tr>
                                            <td>#<?php echo $sale_item->id; ?></td>
                                            <td><?php echo date('M d, Y H:i', strtotime($sale_item->sale_date)); ?></td>
                                            <td><?php echo htmlspecialchars($sale_item->cashier_name ?? 'N/A'); ?></td>
                                            <td><?php echo htmlspecialchars($sale_item->customer_name ?? 'Walk-in'); ?></td>
                                            <td><?php echo $sale_item->items_count; ?></td>
                                            <td>$<?php echo number_format($sale_item->total_amount, 2); ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo $sale_item->payment_type == 'cash' ? 'success' : 'primary'; ?>">
                                                    <?php echo ucfirst($sale_item->payment_type); ?>
                                                </span>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="7" class="text-center text-muted">No sales found for the selected period</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- Top Products -->
                <div class="card">
                    <div class="card-header">
                        <h5>Top Selling Products</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-sm">
                                <thead>
                                    <tr>
                                        <th>Product</th>
                                        <th>Quantity Sold</th>
                                        <th>Revenue</th>
                                        <th>Current Stock</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if($top_products): ?>
                                        <?php foreach($top_products as $product_item): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($product_item->name); ?></td>
                                            <td><?php echo $product_item->total_sold ?? 0; ?></td>
                                            <td>$<?php echo number_format($product_item->total_revenue ?? 0, 2); ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo ($product_item->stock <= $product_item->min_stock) ? 'danger' : 'success'; ?>">
                                                    <?php echo $product_item->stock; ?>
                                                </span>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="4" class="text-center text-muted">No product sales data available</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../../assets/js/script.js"></script>
</body>
</html>
