<?php
require_once '../../../includes/auth.php';
if(!isAdmin()) {
    header('Location: ../../cashier/pos.php');
    exit;
}

require_once '../../../classes/Report.php';

$database = new Database();
$db = $database->getConnection();

$report = new Report($db);

// Default to current month
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-01');
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');

// Get report data
$sales = $report->getSalesByDateRange($start_date, $end_date);
$best_sellers = $report->getBestSellingProducts(5, $start_date, $end_date);
$payment_types = $report->getSalesByPaymentType($start_date, $end_date);

// Calculate totals
$total_sales = array_sum(array_column($payment_types, 'total'));
$total_transactions = array_sum(array_column($payment_types, 'count'));
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sales Reports - Sales Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link href="../../../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include '../../../includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include '../../../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Sales Reports</h1>
                </div>
                
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Filter Reports</h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-5">
                                <label for="start_date" class="form-label">Start Date</label>
                                <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo $start_date; ?>">
                            </div>
                            <div class="col-md-5">
                                <label for="end_date" class="form-label">End Date</label>
                                <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo $end_date; ?>">
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary">Filter</button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card text-white bg-primary mb-3">
                            <div class="card-body">
                                <h5 class="card-title">Total Sales</h5>
                                <p class="card-text display-6">$<?php echo number_format($total_sales, 2); ?></p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card text-white bg-success mb-3">
                            <div class="card-body">
                                <h5 class="card-title">Total Transactions</h5>
                                <p class="card-text display-6"><?php echo $total_transactions; ?></p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card text-white bg-info mb-3">
                            <div class="card-body">
                                <h5 class="card-title">Average Sale</h5>
                                <p class="card-text display-6">$<?php echo $total_transactions > 0 ? number_format($total_sales / $total_transactions, 2) : '0.00'; ?></p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5>Sales by Payment Type</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-sm">
                                        <thead>
                                            <tr>
                                                <th>Payment Type</th>
                                                <th>Transactions</th>
                                                <th>Amount</th>
                                                <th>% of Total</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach($payment_types as $type): ?>
                                            <tr>
                                                <td><?php echo ucfirst($type->payment_type); ?></td>
                                                <td><?php echo $type->count; ?></td>
                                                <td>$<?php echo number_format($type->total, 2); ?></td>
                                                <td><?php echo $total_sales > 0 ? number_format(($type->total / $total_sales) * 100, 2) : '0'; ?>%</td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5>Best Selling Products</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-sm">
                                        <thead>
                                            <tr>
                                                <th>Product</th>
                                                <th>Quantity Sold</th>
                                                <th>Total Sales</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach($best_sellers as $product): ?>
                                            <tr>
                                                <td><?php echo $product->name; ?></td>
                                                <td><?php echo $product->total_quantity; ?></td>
                                                <td>$<?php echo number_format($product->total_sales, 2); ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5>Sales Details</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-sm">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Date</th>
                                        <th>Customer</th>
                                        <th>Cashier</th>
                                        <th>Payment</th>
                                        <th>Amount</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach($sales as $sale): ?>
                                    <tr>
                                        <td><?php echo $sale->id; ?></td>
                                        <td><?php echo date('M j, Y h:i A', strtotime($sale->sale_date)); ?></td>
                                        <td><?php echo $sale->customer_name ?? 'Walk-in'; ?></td>
                                        <td><?php echo $sale->cashier_name; ?></td>
                                        <td><?php echo ucfirst($sale->payment_type); ?></td>
                                        <td>$<?php echo number_format($sale->total_amount, 2); ?></td>
                                        <td>
                                            <a href="../sales/view.php?id=<?php echo $sale->id; ?>" class="btn btn-sm btn-info">View</a>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../../../assets/js/script.js"></script>
</body>
</html>