<?php
// Ensure auth is loaded
if (!function_exists('getCurrentUser')) {
    require_once 'auth.php';
}

$current_user = getCurrentUser();
$root_path = getRootPath();
?>

<nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
    <div class="container-fluid">
        <a class="navbar-brand" href="<?php echo $root_path; ?>">
            <i class="bi bi-shop"></i> Sales Management
        </a>
        
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <?php if(isAdmin()): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo $root_path; ?>admin/dashboard.php">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="productsDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-box-seam"></i> Products
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="<?php echo $root_path; ?>admin/products/index.php">All Products</a></li>
                            <li><a class="dropdown-item" href="<?php echo $root_path; ?>admin/products/add.php">Add Product</a></li>
                            <li><a class="dropdown-item" href="<?php echo $root_path; ?>admin/categories/index.php">Categories</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="salesDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-cart-check"></i> Sales
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="<?php echo $root_path; ?>cashier/pos.php">POS System</a></li>
                            <li><a class="dropdown-item" href="<?php echo $root_path; ?>admin/sales/index.php">All Sales</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="customersDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-people"></i> Customers
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="<?php echo $root_path; ?>admin/customers/index.php">All Customers</a></li>
                            <li><a class="dropdown-item" href="<?php echo $root_path; ?>admin/customers/add.php">Add Customer</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="reportsDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-graph-up"></i> Reports
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="<?php echo $root_path; ?>admin/reports/sales.php">Sales Reports</a></li>
                            <li><a class="dropdown-item" href="<?php echo $root_path; ?>admin/reports/products.php">Product Reports</a></li>
                            <li><a class="dropdown-item" href="<?php echo $root_path; ?>admin/reports/customers.php">Customer Reports</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="usersDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-gear"></i> Users
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="<?php echo $root_path; ?>admin/users/index.php">All Users</a></li>
                            <li><a class="dropdown-item" href="<?php echo $root_path; ?>admin/users/add.php">Add User</a></li>
                        </ul>
                    </li>
                <?php else: ?>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo $root_path; ?>cashier/pos.php">
                            <i class="bi bi-cart-check"></i> POS System
                        </a>
                    </li>
                <?php endif; ?>
            </ul>
            
            <ul class="navbar-nav">
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle"></i> 
                        <?php echo $current_user ? $current_user->full_name : 'User'; ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="<?php echo $root_path; ?>profile.php">
                            <i class="bi bi-person"></i> Profile
                        </a></li>
                        <li><a class="dropdown-item" href="<?php echo $root_path; ?>settings.php">
                            <i class="bi bi-gear"></i> Settings
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="<?php echo $root_path; ?>logout.php">
                            <i class="bi bi-box-arrow-right"></i> Logout
                        </a></li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>

<style>
body {
    padding-top: 76px; /* Account for fixed navbar */
}
</style>
