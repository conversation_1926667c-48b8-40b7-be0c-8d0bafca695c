<?php
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Determine the correct path to root directory
$root_path = '';
$current_dir = dirname($_SERVER['SCRIPT_FILENAME']);
$doc_root = $_SERVER['DOCUMENT_ROOT'];

// Find the relative path to the shop directory
if (strpos($current_dir, '/admin/') !== false) {
    $root_path = '../../';
} elseif (strpos($current_dir, '/cashier/') !== false) {
    $root_path = '../../';
} elseif (strpos($current_dir, '/includes/') !== false) {
    $root_path = '../';
} else {
    $root_path = './';
}

require_once $root_path . 'config/database.php';
require_once $root_path . 'classes/Database.php';
require_once $root_path . 'classes/User.php';

$database = new Database();
$db = $database->getConnection();

$user = new User($db);
$current_user = null;

if(isset($_SESSION['user_id'])) {
    $user->id = $_SESSION['user_id'];
    $current_user = $user->readOne();

    if(!$current_user) {
        // Clear invalid session
        session_destroy();
        $login_url = $root_path . 'login.php';
        header('Location: ' . $login_url);
        exit;
    }
} else {
    // If not login page or index page, redirect to login
    $current_file = basename($_SERVER['PHP_SELF']);
    if($current_file != 'login.php' && $current_file != 'index.php') {
        $login_url = $root_path . 'login.php';
        header('Location: ' . $login_url);
        exit;
    }
}

function isAdmin() {
    global $current_user;
    return isset($current_user) && $current_user->role == 'admin';
}

function isCashier() {
    global $current_user;
    return isset($current_user) && $current_user->role == 'cashier';
}

function isLoggedIn() {
    global $current_user;
    return isset($current_user) && $current_user;
}

function getCurrentUser() {
    global $current_user;
    return $current_user;
}

function logout() {
    session_start();
    session_destroy();
    header('Location: ' . getRootPath() . 'login.php');
    exit;
}

function getRootPath() {
    global $root_path;
    return $root_path;
}
?>