<?php
session_start();
require_once '../config/database.php';
require_once '../classes/User.php';

$database = new Database();
$db = $database->getConnection();

$user = new User($db);

if(isset($_SESSION['user_id'])) {
    $user->id = $_SESSION['user_id'];
    $current_user = $user->readOne();
    
    if(!$current_user) {
        header('Location: ../login.php');
        exit;
    }
} else {
    // If not login page, redirect to login
    if(basename($_SERVER['PHP_SELF']) != 'login.php') {
        header('Location: login.php');
        exit;
    }
}

function isAdmin() {
    global $current_user;
    return isset($current_user) && $current_user->role == 'admin';
}

function isCashier() {
    global $current_user;
    return isset($current_user) && $current_user->role == 'cashier';
}
?>