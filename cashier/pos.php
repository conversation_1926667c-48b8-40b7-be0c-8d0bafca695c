<?php
require_once '../../includes/auth.php';

require_once '../../classes/Product.php';
require_once '../../classes/Customer.php';
require_once '../../classes/Sale.php';

$database = new Database();
$db = $database->getConnection();

$product = new Product($db);
$customer = new Customer($db);
$sale = new Sale($db);

// Handle POS submission
if($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['complete_sale'])) {
    $sale->user_id = $_SESSION['user_id'];
    $sale->customer_id = $_POST['customer_id'] ?: null;
    $sale->total_amount = $_POST['total_amount'];
    $sale->amount_received = $_POST['amount_received'];
    $sale->change_amount = $_POST['change_amount'];
    $sale->discount = $_POST['discount'] ?: 0;
    $sale->payment_type = $_POST['payment_type'];
    
    $cart_items = json_decode($_POST['cart_items'], true);
    
    if($sale_id = $sale->create($cart_items)) {
        $_SESSION['message'] = "Sale completed successfully. Receipt #$sale_id";
        header("Location: pos.php");
        exit();
    } else {
        $error = "Failed to complete sale. Please try again.";
    }
}

// Get products and customers for dropdowns
$products = $product->read();
$customers = $customer->read();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>POS - Sales Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link href="../../assets/css/pos.css" rel="stylesheet">
</head>
<body>
    <?php include '../../includes/header.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include '../../includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Point of Sale</h1>
                </div>
                
                <?php if(isset($_SESSION['message'])): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $_SESSION['message']; unset($_SESSION['message']); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <?php if(isset($error)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $error; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <div class="row">
                    <div class="col-md-8">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5>Products</h5>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <input type="text" id="productSearch" class="form-control" placeholder="Search products...">
                                    </div>
                                    <div class="col-md-6">
                                        <select id="categoryFilter" class="form-select">
                                            <option value="">All Categories</option>
                                            <?php foreach($categories as $category): ?>
                                                <option value="<?php echo $category->id; ?>"><?php echo $category->name; ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="row" id="productGrid">
                                    <?php foreach($products as $product): ?>
                                        <div class="col-md-3 mb-3 product-item" 
                                             data-id="<?php echo $product->id; ?>" 
                                             data-name="<?php echo htmlspecialchars($product->name); ?>" 
                                             data-price="<?php echo $product->price; ?>" 
                                             data-stock="<?php echo $product->stock; ?>"
                                             data-category="<?php echo $product->category_id; ?>">
                                            <div class="card h-100 product-card">
                                                <div class="card-body text-center">
                                                    <h6 class="card-title"><?php echo $product->name; ?></h6>
                                                    <p class="card-text text-success">$<?php echo number_format($product->price, 2); ?></p>
                                                    <p class="card-text small <?php echo $product->stock < $product->min_stock ? 'text-danger' : 'text-muted'; ?>">
                                                        Stock: <?php echo $product->stock; ?>
                                                    </p>
                                                    <button class="btn btn-sm btn-primary add-to-cart">Add to Cart</button>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5>Sale Summary</h5>
                            </div>
                            <div class="card-body">
                                <form id="posForm" method="POST">
                                    <div class="mb-3">
                                        <label for="customer_id" class="form-label">Customer</label>
                                        <select class="form-select" id="customer_id" name="customer_id">
                                            <option value="">Walk-in Customer</option>
                                            <?php foreach($customers as $customer): ?>
                                                <option value="<?php echo $customer->id; ?>"><?php echo $customer->name; ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    
                                    <div class="table-responsive mb-3">
                                        <table class="table table-sm" id="cartTable">
                                            <thead>
                                                <tr>
                                                    <th>Product</th>
                                                    <th>Qty</th>
                                                    <th>Price</th>
                                                    <th>Total</th>
                                                    <th></th>
                                                </tr>
                                            </thead>
                                            <tbody id="cartItems">
                                                <!-- Cart items will be added here dynamically -->
                                            </tbody>
                                            <tfoot>
                                                <tr>
                                                    <td colspan="3" class="text-end"><strong>Subtotal:</strong></td>
                                                    <td id="subtotal">$0.00</td>
                                                    <td></td>
                                                </tr>
                                                <tr>
                                                    <td colspan="3" class="text-end"><strong>Discount:</strong></td>
                                                    <td>
                                                        <input type="number" id="discount" name="discount" class="form-control form-control-sm" value="0" min="0" step="0.01">
                                                    </td>
                                                    <td></td>
                                                </tr>
                                                <tr>
                                                    <td colspan="3" class="text-end"><strong>Total:</strong></td>
                                                    <td id="totalAmount">$0.00</td>
                                                    <td></td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="payment_type" class="form-label">Payment Type</label>
                                        <select class="form-select" id="payment_type" name="payment_type" required>
                                            <option value="cash">Cash</option>
                                            <option value="card">Card</option>
                                            <option value="transfer">Bank Transfer</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="amount_received" class="form-label">Amount Received</label>
                                        <input type="number" class="form-control" id="amount_received" name="amount_received" required min="0" step="0.01">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="change_amount" class="form-label">Change</label>
                                        <input type="number" class="form-control" id="change_amount" name="change_amount" readonly>
                                    </div>
                                    
                                    <input type="hidden" id="cart_items" name="cart_items">
                                    <input type="hidden" id="total_amount" name="total_amount">
                                    
                                    <div class="d-grid">
                                        <button type="submit" name="complete_sale" class="btn btn-success btn-lg">
                                            <i class="bi bi-check-circle"></i> Complete Sale
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../../assets/js/pos.js"></script>
</body>
</html>