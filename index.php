<?php
session_start();

// If user is already logged in, redirect to appropriate dashboard
if(isset($_SESSION['user_id'])) {
    if($_SESSION['role'] == 'admin') {
        header('Location: admin/dashboard.php');
    } else {
        header('Location: cashier/pos.php');
    }
    exit;
}

// Include database configuration
require_once 'config/database.php';

// Handle login if form is submitted
$login_err = '';
if($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['username'])) {
    require_once 'classes/User.php';
    
    $database = new Database();
    $db = $database->getConnection();
    
    $user = new User($db);
    $user->username = $_POST['username'];
    $user->password = $_POST['password'];
    
    if($user->login()) {
        $_SESSION['user_id'] = $user->id;
        $_SESSION['username'] = $user->username;
        $_SESSION['role'] = $user->role;
        
        if($user->role == 'admin') {
            header('Location: admin/dashboard.php');
        } else {
            header('Location: cashier/pos.php');
        }
        exit;
    } else {
        $login_err = 'Invalid username or password.';
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome - Sales Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link href="assets/css/style.css" rel="stylesheet">
    <style>
        .hero-section {
            background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('assets/images/shop-bg.jpg');
            background-size: cover;
            background-position: center;
            color: white;
            padding: 100px 0;
            margin-bottom: 50px;
        }
        .feature-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: #0d6efd;
        }
        .login-card {
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border: none;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="bi bi-shop"></i> Sales Management
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#features">Features</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#about">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="login.php">Login</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section text-center">
        <div class="container">
            <h1 class="display-4 fw-bold mb-4">Streamline Your Retail Business</h1>
            <p class="lead mb-5">Powerful sales management solution for shops and retailers</p>
            <a href="#login" class="btn btn-primary btn-lg px-4 me-2">
                <i class="bi bi-box-arrow-in-right"></i> Get Started
            </a>
            <a href="#features" class="btn btn-outline-light btn-lg px-4">
                <i class="bi bi-info-circle"></i> Learn More
            </a>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-5 bg-light">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="fw-bold">Key Features</h2>
                <p class="lead text-muted">Everything you need to manage your retail business efficiently</p>
            </div>
            
            <div class="row g-4">
                <div class="col-md-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body text-center p-4">
                            <div class="feature-icon">
                                <i class="bi bi-cart-check"></i>
                            </div>
                            <h4 class="card-title">Point of Sale</h4>
                            <p class="card-text">Fast and intuitive POS system with barcode scanning, discounts, and multiple payment options.</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body text-center p-4">
                            <div class="feature-icon">
                                <i class="bi bi-box-seam"></i>
                            </div>
                            <h4 class="card-title">Inventory Management</h4>
                            <p class="card-text">Track stock levels, get low stock alerts, and manage product categories with ease.</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body text-center p-4">
                            <div class="feature-icon">
                                <i class="bi bi-people"></i>
                            </div>
                            <h4 class="card-title">Customer Management</h4>
                            <p class="card-text">Track customer purchases, manage contact information, and build customer loyalty.</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body text-center p-4">
                            <div class="feature-icon">
                                <i class="bi bi-graph-up"></i>
                            </div>
                            <h4 class="card-title">Sales Reports</h4>
                            <p class="card-text">Generate detailed reports on sales performance, best-selling products, and revenue trends.</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body text-center p-4">
                            <div class="feature-icon">
                                <i class="bi bi-receipt"></i>
                            </div>
                            <h4 class="card-title">Receipt Printing</h4>
                            <p class="card-text">Print professional receipts with your shop logo and all transaction details.</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body text-center p-4">
                            <div class="feature-icon">
                                <i class="bi bi-phone"></i>
                            </div>
                            <h4 class="card-title">Mobile-Friendly</h4>
                            <p class="card-text">Access your sales data from any device with our responsive interface.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Login Section -->
    <section id="login" class="py-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="card login-card">
                        <div class="card-body p-5">
                            <h3 class="card-title text-center mb-4">Login to Your Account</h3>
                            
                            <?php if(!empty($login_err)): ?>
                                <div class="alert alert-danger"><?php echo $login_err; ?></div>
                            <?php endif; ?>
                            
                            <form action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>" method="post">
                                <div class="mb-3">
                                    <label for="username" class="form-label">Username</label>
                                    <input type="text" class="form-control" id="username" name="username" required>
                                </div>
                                <div class="mb-3">
                                    <label for="password" class="form-label">Password</label>
                                    <input type="password" class="form-control" id="password" name="password" required>
                                </div>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">Login</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-5 bg-dark text-white">
        <div class="container">
            <div class="row">
                <div class="col-lg-6">
                    <h2 class="fw-bold mb-4">About Our System</h2>
                    <p class="lead">The Sales Management System is designed to help retail businesses streamline their operations, improve efficiency, and gain valuable insights into their sales performance.</p>
                    <p>Our solution provides all the tools you need to manage your shop effectively, from inventory tracking to customer management and comprehensive reporting.</p>
                </div>
                <div class="col-lg-6">
                    <h2 class="fw-bold mb-4">Why Choose Us?</h2>
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="bi bi-check-circle-fill text-primary me-2"></i> Easy-to-use interface</li>
                        <li class="mb-2"><i class="bi bi-check-circle-fill text-primary me-2"></i> No monthly fees - one-time payment</li>
                        <li class="mb-2"><i class="bi bi-check-circle-fill text-primary me-2"></i> Regular updates and improvements</li>
                        <li class="mb-2"><i class="bi bi-check-circle-fill text-primary me-2"></i> Dedicated customer support</li>
                        <li class="mb-2"><i class="bi bi-check-circle-fill text-primary me-2"></i> Cloud or self-hosted options</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="py-4 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0">&copy; <?php echo date('Y'); ?> Sales Management System. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="#" class="text-decoration-none me-3">Privacy Policy</a>
                    <a href="#" class="text-decoration-none me-3">Terms of Service</a>
                    <a href="#" class="text-decoration-none">Contact Us</a>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
    </script>
</body>
</html>