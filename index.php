<?php
session_start();

// If user is already logged in, redirect to appropriate dashboard
if(isset($_SESSION['user_id'])) {
    if($_SESSION['role'] == 'admin') {
        header('Location: admin/dashboard.php');
    } else {
        header('Location: cashier/pos.php');
    }
    exit;
}

// Include database configuration
require_once 'includes/config.php';

// Handle login if form is submitted
$login_err = '';
if($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['username'])) {
    require_once 'classes/User.php';

    $database = new Database();
    $db = $database->getConnection();

    $user = new User($db);
    $user->username = $_POST['username'];
    $user->password = $_POST['password'];

    if($user->login()) {
        $_SESSION['user_id'] = $user->id;
        $_SESSION['username'] = $user->username;
        $_SESSION['role'] = $user->role;

        if($user->role == 'admin') {
            header('Location: admin/dashboard.php');
        } else {
            header('Location: cashier/pos.php');
        }
        exit;
    } else {
        $login_err = 'Invalid username or password.';
    }
}

// Get some basic stats for the homepage
$stats = [
    'total_products' => 0,
    'total_sales' => 0,
    'total_customers' => 0,
    'total_revenue' => 0
];

try {
    $database = new Database();
    $db = $database->getConnection();

    // Get product count
    $stmt = $db->query("SELECT COUNT(*) as count FROM products");
    $stats['total_products'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

    // Get sales count
    $stmt = $db->query("SELECT COUNT(*) as count FROM sales");
    $stats['total_sales'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

    // Get customer count
    $stmt = $db->query("SELECT COUNT(*) as count FROM customers");
    $stats['total_customers'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

    // Get total revenue
    $stmt = $db->query("SELECT SUM(total_amount) as revenue FROM sales");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $stats['total_revenue'] = $result['revenue'] ?? 0;

} catch(Exception $e) {
    // If database is not set up yet, keep default values
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sales Management System - Modern POS & Inventory Solution</title>
    <meta name="description" content="Complete sales management system with POS, inventory tracking, customer management, and detailed reporting.">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e40af;
            --accent-color: #3b82f6;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
        }

        .hero-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 120px 0 80px;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon fill="rgba(255,255,255,0.05)" points="0,1000 1000,0 1000,1000"/></svg>');
            background-size: cover;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .feature-card {
            transition: all 0.3s ease;
            border: none;
            border-radius: 16px;
            overflow: hidden;
            height: 100%;
        }

        .feature-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1.5rem;
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            color: white;
            padding: 2rem;
            text-align: center;
            margin-bottom: 1rem;
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .login-card {
            border: none;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .btn-custom {
            border-radius: 12px;
            padding: 12px 30px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }

        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }

        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }

        .shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 60%;
            right: 10%;
            animation-delay: 2s;
        }

        .shape:nth-child(3) {
            width: 60px;
            height: 60px;
            top: 40%;
            left: 80%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .demo-credentials {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top" style="background: rgba(31, 41, 55, 0.95); backdrop-filter: blur(10px);">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="bi bi-shop-window"></i> SalesFlow Pro
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#features">Features</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#stats">Statistics</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#about">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link btn btn-outline-light btn-sm ms-2 px-3" href="#login">
                            <i class="bi bi-box-arrow-in-right"></i> Login
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section text-center">
        <div class="floating-shapes">
            <div class="shape"></div>
            <div class="shape"></div>
            <div class="shape"></div>
        </div>
        <div class="container hero-content">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <h1 class="display-3 fw-bold mb-4">Transform Your Retail Business</h1>
                    <p class="lead mb-5 fs-4">Complete sales management solution with modern POS system, inventory tracking, and powerful analytics</p>
                    <div class="d-flex flex-column flex-sm-row gap-3 justify-content-center">
                        <a href="#login" class="btn btn-light btn-custom btn-lg">
                            <i class="bi bi-rocket-takeoff"></i> Start Now
                        </a>
                        <a href="#features" class="btn btn-outline-light btn-custom btn-lg">
                            <i class="bi bi-play-circle"></i> Watch Demo
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Statistics Section -->
    <section id="stats" class="py-5" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="section-title text-white">System Overview</h2>
                <p class="lead text-white-50">Real-time statistics from our sales management system</p>
            </div>

            <div class="row g-4">
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="stats-number"><?php echo number_format($stats['total_products']); ?></div>
                        <div class="stats-label">
                            <i class="bi bi-box-seam me-2"></i>Products
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="stats-number"><?php echo number_format($stats['total_sales']); ?></div>
                        <div class="stats-label">
                            <i class="bi bi-receipt me-2"></i>Sales
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="stats-number"><?php echo number_format($stats['total_customers']); ?></div>
                        <div class="stats-label">
                            <i class="bi bi-people me-2"></i>Customers
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="stats-number">$<?php echo number_format($stats['total_revenue'], 0); ?></div>
                        <div class="stats-label">
                            <i class="bi bi-currency-dollar me-2"></i>Revenue
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-5 bg-light">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="section-title">Powerful Features</h2>
                <p class="lead text-muted">Everything you need to manage your retail business efficiently</p>
            </div>

            <div class="row g-4">
                <div class="col-lg-4 col-md-6">
                    <div class="card feature-card shadow-sm">
                        <div class="card-body text-center p-4">
                            <div class="feature-icon">
                                <i class="bi bi-cart-check-fill"></i>
                            </div>
                            <h4 class="card-title fw-bold">Smart POS System</h4>
                            <p class="card-text text-muted">Lightning-fast checkout with barcode scanning, multiple payment methods, and real-time inventory updates.</p>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="card feature-card shadow-sm">
                        <div class="card-body text-center p-4">
                            <div class="feature-icon">
                                <i class="bi bi-boxes"></i>
                            </div>
                            <h4 class="card-title fw-bold">Inventory Control</h4>
                            <p class="card-text text-muted">Advanced stock management with low-stock alerts, category organization, and automated reorder points.</p>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="card feature-card shadow-sm">
                        <div class="card-body text-center p-4">
                            <div class="feature-icon">
                                <i class="bi bi-people-fill"></i>
                            </div>
                            <h4 class="card-title fw-bold">Customer Hub</h4>
                            <p class="card-text text-muted">Comprehensive customer database with purchase history, loyalty tracking, and personalized service.</p>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="card feature-card shadow-sm">
                        <div class="card-body text-center p-4">
                            <div class="feature-icon">
                                <i class="bi bi-graph-up-arrow"></i>
                            </div>
                            <h4 class="card-title fw-bold">Analytics & Reports</h4>
                            <p class="card-text text-muted">Detailed sales analytics, profit margins, top-selling products, and customizable business reports.</p>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="card feature-card shadow-sm">
                        <div class="card-body text-center p-4">
                            <div class="feature-icon">
                                <i class="bi bi-shield-check"></i>
                            </div>
                            <h4 class="card-title fw-bold">Secure & Reliable</h4>
                            <p class="card-text text-muted">Bank-level security with role-based access, data encryption, and automatic backups.</p>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="card feature-card shadow-sm">
                        <div class="card-body text-center p-4">
                            <div class="feature-icon">
                                <i class="bi bi-phone-fill"></i>
                            </div>
                            <h4 class="card-title fw-bold">Mobile Ready</h4>
                            <p class="card-text text-muted">Fully responsive design that works perfectly on tablets, phones, and desktop computers.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Login Section -->
    <section id="login" class="py-5">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="section-title">Access Your Dashboard</h2>
                <p class="lead text-muted">Login to manage your sales, inventory, and customers</p>
            </div>

            <div class="row justify-content-center">
                <div class="col-lg-5 col-md-7">
                    <!-- Demo Credentials -->
                    <div class="demo-credentials">
                        <h5 class="fw-bold mb-3">
                            <i class="bi bi-key-fill me-2"></i>Demo Credentials
                        </h5>
                        <div class="row">
                            <div class="col-6">
                                <strong>Admin Access:</strong><br>
                                <small>Username: <code>admin</code><br>
                                Password: <code>admin123</code></small>
                            </div>
                            <div class="col-6">
                                <strong>Cashier Access:</strong><br>
                                <small>Username: <code>cashier</code><br>
                                Password: <code>cashier123</code></small>
                            </div>
                        </div>
                    </div>

                    <div class="card login-card">
                        <div class="card-body p-5">
                            <div class="text-center mb-4">
                                <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                                    <i class="bi bi-person-circle text-white" style="font-size: 2.5rem;"></i>
                                </div>
                                <h3 class="mt-3 fw-bold">Welcome Back</h3>
                                <p class="text-muted">Sign in to your account</p>
                            </div>

                            <?php if(!empty($login_err)): ?>
                                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                    <i class="bi bi-exclamation-triangle-fill me-2"></i><?php echo $login_err; ?>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            <?php endif; ?>

                            <form action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>" method="post">
                                <div class="mb-4">
                                    <label for="username" class="form-label fw-semibold">Username</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-person"></i>
                                        </span>
                                        <input type="text" class="form-control form-control-lg" id="username" name="username" placeholder="Enter your username" required>
                                    </div>
                                </div>
                                <div class="mb-4">
                                    <label for="password" class="form-label fw-semibold">Password</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-lock"></i>
                                        </span>
                                        <input type="password" class="form-control form-control-lg" id="password" name="password" placeholder="Enter your password" required>
                                    </div>
                                </div>
                                <div class="d-grid mb-3">
                                    <button type="submit" class="btn btn-primary btn-lg btn-custom">
                                        <i class="bi bi-box-arrow-in-right me-2"></i>Sign In
                                    </button>
                                </div>
                            </form>

                            <div class="text-center">
                                <small class="text-muted">
                                    Don't have an account? Contact your administrator.
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-5" style="background: linear-gradient(135deg, var(--dark-color) 0%, #374151 100%);">
        <div class="container text-white">
            <div class="row align-items-center">
                <div class="col-lg-6 mb-5 mb-lg-0">
                    <h2 class="section-title text-white mb-4">Built for Modern Retail</h2>
                    <p class="lead mb-4">SalesFlow Pro is a comprehensive sales management solution designed specifically for retail businesses who want to modernize their operations and boost profitability.</p>
                    <p class="mb-4">From small boutiques to large retail chains, our system adapts to your business needs with powerful features that grow with you.</p>

                    <div class="row g-3">
                        <div class="col-6">
                            <div class="d-flex align-items-center">
                                <div class="bg-primary rounded-circle p-2 me-3">
                                    <i class="bi bi-lightning-charge text-white"></i>
                                </div>
                                <div>
                                    <div class="fw-bold">Fast Setup</div>
                                    <small class="text-white-50">Ready in minutes</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="d-flex align-items-center">
                                <div class="bg-success rounded-circle p-2 me-3">
                                    <i class="bi bi-shield-check text-white"></i>
                                </div>
                                <div>
                                    <div class="fw-bold">Secure</div>
                                    <small class="text-white-50">Bank-level security</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="row g-4">
                        <div class="col-12">
                            <div class="card bg-white bg-opacity-10 border-0 text-white">
                                <div class="card-body p-4">
                                    <h5 class="card-title">
                                        <i class="bi bi-award-fill text-warning me-2"></i>Why Choose SalesFlow Pro?
                                    </h5>
                                    <ul class="list-unstyled mb-0">
                                        <li class="mb-2">
                                            <i class="bi bi-check-circle-fill text-success me-2"></i>
                                            Intuitive user interface designed for speed
                                        </li>
                                        <li class="mb-2">
                                            <i class="bi bi-check-circle-fill text-success me-2"></i>
                                            Real-time inventory synchronization
                                        </li>
                                        <li class="mb-2">
                                            <i class="bi bi-check-circle-fill text-success me-2"></i>
                                            Advanced reporting and analytics
                                        </li>
                                        <li class="mb-2">
                                            <i class="bi bi-check-circle-fill text-success me-2"></i>
                                            Multi-user support with role management
                                        </li>
                                        <li class="mb-2">
                                            <i class="bi bi-check-circle-fill text-success me-2"></i>
                                            24/7 customer support and training
                                        </li>
                                        <li class="mb-0">
                                            <i class="bi bi-check-circle-fill text-success me-2"></i>
                                            Regular updates and new features
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="py-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4 mb-lg-0">
                    <h5 class="fw-bold mb-3">
                        <i class="bi bi-shop-window text-primary me-2"></i>SalesFlow Pro
                    </h5>
                    <p class="text-muted mb-3">Modern sales management solution for retail businesses of all sizes.</p>
                    <div class="d-flex gap-2">
                        <a href="#" class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-facebook"></i>
                        </a>
                        <a href="#" class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-twitter"></i>
                        </a>
                        <a href="#" class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-linkedin"></i>
                        </a>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6 mb-4 mb-lg-0">
                    <h6 class="fw-bold mb-3">Product</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="#features" class="text-decoration-none text-muted">Features</a></li>
                        <li class="mb-2"><a href="#" class="text-decoration-none text-muted">Pricing</a></li>
                        <li class="mb-2"><a href="#" class="text-decoration-none text-muted">Demo</a></li>
                        <li class="mb-2"><a href="#" class="text-decoration-none text-muted">Updates</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6 mb-4 mb-lg-0">
                    <h6 class="fw-bold mb-3">Support</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="#" class="text-decoration-none text-muted">Help Center</a></li>
                        <li class="mb-2"><a href="#" class="text-decoration-none text-muted">Documentation</a></li>
                        <li class="mb-2"><a href="#" class="text-decoration-none text-muted">Contact Us</a></li>
                        <li class="mb-2"><a href="#" class="text-decoration-none text-muted">Training</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6 mb-4 mb-lg-0">
                    <h6 class="fw-bold mb-3">Company</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="#about" class="text-decoration-none text-muted">About</a></li>
                        <li class="mb-2"><a href="#" class="text-decoration-none text-muted">Blog</a></li>
                        <li class="mb-2"><a href="#" class="text-decoration-none text-muted">Careers</a></li>
                        <li class="mb-2"><a href="#" class="text-decoration-none text-muted">Partners</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6">
                    <h6 class="fw-bold mb-3">Legal</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="#" class="text-decoration-none text-muted">Privacy</a></li>
                        <li class="mb-2"><a href="#" class="text-decoration-none text-muted">Terms</a></li>
                        <li class="mb-2"><a href="#" class="text-decoration-none text-muted">Security</a></li>
                        <li class="mb-2"><a href="#" class="text-decoration-none text-muted">Compliance</a></li>
                    </ul>
                </div>
            </div>
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0 text-muted">&copy; <?php echo date('Y'); ?> SalesFlow Pro. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <small class="text-muted">
                        Made with <i class="bi bi-heart-fill text-danger"></i> for retail businesses
                    </small>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Navbar background change on scroll
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.style.background = 'rgba(31, 41, 55, 0.98)';
            } else {
                navbar.style.background = 'rgba(31, 41, 55, 0.95)';
            }
        });

        // Auto-fill demo credentials
        function fillDemoCredentials(type) {
            const usernameField = document.getElementById('username');
            const passwordField = document.getElementById('password');

            if (type === 'admin') {
                usernameField.value = 'admin';
                passwordField.value = 'admin123';
            } else if (type === 'cashier') {
                usernameField.value = 'cashier';
                passwordField.value = 'cashier123';
            }
        }

        // Add click handlers to demo credentials
        document.addEventListener('DOMContentLoaded', function() {
            // Make demo credentials clickable
            const demoCredentials = document.querySelector('.demo-credentials');
            if (demoCredentials) {
                const adminSection = demoCredentials.querySelector('.col-6:first-child');
                const cashierSection = demoCredentials.querySelector('.col-6:last-child');

                adminSection.style.cursor = 'pointer';
                cashierSection.style.cursor = 'pointer';

                adminSection.addEventListener('click', () => fillDemoCredentials('admin'));
                cashierSection.addEventListener('click', () => fillDemoCredentials('cashier'));

                // Add hover effects
                adminSection.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
                    this.style.borderRadius = '8px';
                    this.style.padding = '8px';
                });

                adminSection.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = 'transparent';
                    this.style.padding = '0';
                });

                cashierSection.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
                    this.style.borderRadius = '8px';
                    this.style.padding = '8px';
                });

                cashierSection.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = 'transparent';
                    this.style.padding = '0';
                });
            }
        });

        // Animate statistics on scroll
        function animateStats() {
            const statsCards = document.querySelectorAll('.stats-number');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const target = entry.target;
                        const finalValue = parseInt(target.textContent.replace(/[^0-9]/g, ''));
                        let currentValue = 0;
                        const increment = finalValue / 50;

                        const timer = setInterval(() => {
                            currentValue += increment;
                            if (currentValue >= finalValue) {
                                currentValue = finalValue;
                                clearInterval(timer);
                            }

                            if (target.textContent.includes('$')) {
                                target.textContent = '$' + Math.floor(currentValue).toLocaleString();
                            } else {
                                target.textContent = Math.floor(currentValue).toLocaleString();
                            }
                        }, 20);

                        observer.unobserve(target);
                    }
                });
            });

            statsCards.forEach(card => observer.observe(card));
        }

        // Initialize animations when page loads
        document.addEventListener('DOMContentLoaded', animateStats);

        // Add loading animation to login form
        document.querySelector('form').addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Signing In...';
            submitBtn.disabled = true;

            // Re-enable button after 3 seconds in case of error
            setTimeout(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 3000);
        });
    </script>
</body>
</html>