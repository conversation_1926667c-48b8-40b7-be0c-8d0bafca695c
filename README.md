# Sales Management System

A comprehensive PHP-based Point of Sale (POS) and inventory management system built with Bootstrap 5 and MySQL.

## Features

### 🏪 Point of Sale (POS)
- Interactive product selection with barcode scanning
- Real-time cart management
- Multiple payment methods (Cash, Card, Mobile)
- Customer selection and management
- Receipt generation and printing
- Change calculation

### 📦 Inventory Management
- Product management with categories
- Stock tracking and low stock alerts
- Barcode generation and management
- Cost and pricing management
- Bulk operations

### 👥 User Management
- Role-based access control (Admin/Cashier)
- Secure authentication system
- User profile management

### 📊 Reporting & Analytics
- Sales reports with date filtering
- Top-selling products analysis
- Revenue and profit tracking
- Customer purchase history

### 🎯 Customer Management
- Customer database
- Purchase history tracking
- Customer search and selection

## Installation

### Prerequisites
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web server (Apache/Nginx)
- XAMPP/LAMPP (recommended for local development)

### Setup Instructions

1. **Clone/Download the project**
   ```bash
   git clone <repository-url>
   # or download and extract to your web server directory
   ```

2. **Configure Database Connection**
   - Edit `includes/config.php`
   - Update database credentials:
     ```php
     private $host = "localhost";
     private $db_name = "shop_management";
     private $username = "your_username";
     private $password = "your_password";
     ```

3. **Run Database Setup**
   - Navigate to `http://your-domain/setup.php`
   - Click "Run Database Setup" to create tables and sample data
   - Or manually import `database_setup.sql` into your MySQL database

4. **Login to the System**
   - Navigate to `http://your-domain/login.php`
   - Use default credentials:
     - **Admin**: username: `admin`, password: `admin123`
     - **Cashier**: username: `cashier`, password: `cashier123`

5. **Change Default Passwords**
   - Login as admin and change default passwords immediately

## File Structure

```
shop/
├── admin/                  # Admin panel pages
│   ├── categories/         # Category management
│   ├── customers/          # Customer management
│   ├── products/           # Product management
│   ├── reports/            # Reports and analytics
│   └── users/              # User management
├── assets/                 # Static assets
│   ├── css/               # Stylesheets
│   └── js/                # JavaScript files
├── cashier/               # Cashier interface
│   └── pos.php            # Point of Sale system
├── classes/               # PHP classes
│   ├── Category.php       # Category management
│   ├── Customer.php       # Customer management
│   ├── Product.php        # Product management
│   ├── Sale.php           # Sales management
│   └── User.php           # User management
├── includes/              # Common includes
│   ├── auth.php           # Authentication functions
│   ├── config.php         # Database configuration
│   ├── header.php         # Common header
│   └── sidebar.php        # Navigation sidebar
├── database_setup.sql     # Database schema and sample data
├── login.php              # Login page
├── logout.php             # Logout handler
├── setup.php              # Database setup page
└── README.md              # This file
```

## Usage

### For Administrators
1. **Dashboard**: Overview of sales, inventory, and system status
2. **Product Management**: Add, edit, delete products and categories
3. **User Management**: Manage cashier accounts and permissions
4. **Customer Management**: Maintain customer database
5. **Reports**: Generate sales reports and analytics
6. **Inventory**: Monitor stock levels and low stock alerts

### For Cashiers
1. **POS System**: Process sales transactions
2. **Product Search**: Find products by name or barcode
3. **Customer Selection**: Associate sales with customers
4. **Payment Processing**: Handle cash, card, and mobile payments
5. **Receipt Printing**: Generate customer receipts

## Key Features Explained

### Authentication System
- Session-based authentication
- Role-based access control
- Secure password hashing
- Automatic session management

### POS System
- Click-to-add product selection
- Barcode scanner support (Enter key simulation)
- Real-time cart updates
- Multiple payment methods
- Change calculation
- Customer association

### Inventory Management
- Stock tracking with alerts
- Category organization
- Barcode generation
- Cost and profit margin calculation
- Low stock notifications

### Reporting
- Date-range filtering
- Sales summary statistics
- Top-selling products
- Revenue analysis
- Printable reports

## Database Schema

### Main Tables
- `users`: System users (admin/cashier)
- `categories`: Product categories
- `products`: Product inventory
- `customers`: Customer database
- `sales`: Sales transactions
- `sale_items`: Individual sale items

### Key Relationships
- Products belong to categories
- Sales are made by users
- Sales can be associated with customers
- Sale items link sales to products

## Security Features

- Password hashing using PHP's `password_hash()`
- SQL injection prevention with prepared statements
- XSS protection with `htmlspecialchars()`
- Session management and timeout
- Role-based access control
- CSRF protection on forms

## Customization

### Adding New Features
1. Create new PHP classes in `classes/` directory
2. Add new pages following the existing structure
3. Update navigation in `includes/sidebar.php`
4. Add corresponding database tables if needed

### Styling
- Modify `assets/css/style.css` for general styling
- Modify `assets/css/pos.css` for POS-specific styling
- Bootstrap 5 classes are used throughout

### JavaScript Functionality
- `assets/js/script.js`: General functionality
- `assets/js/pos.js`: POS-specific functionality

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check `includes/config.php` settings
   - Verify MySQL service is running
   - Ensure database exists

2. **Permission Denied**
   - Check file permissions
   - Ensure web server can read/write files

3. **Session Issues**
   - Check PHP session configuration
   - Verify session directory permissions

4. **JavaScript Not Working**
   - Check browser console for errors
   - Ensure Bootstrap JS is loaded
   - Verify file paths are correct

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review error logs
3. Verify configuration settings
4. Check file permissions

## License

This project is open source and available under the MIT License.

## Version History

- **v1.0**: Initial release with core POS and inventory features
